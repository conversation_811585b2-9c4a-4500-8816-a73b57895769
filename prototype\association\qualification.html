<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资质认定管理 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Microsoft YaHei', sans-serif; }
        .navbar { background: linear-gradient(45deg, #667eea, #764ba2); box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .sidebar { background: white; min-height: calc(100vh - 76px); box-shadow: 2px 0 10px rgba(0,0,0,0.1); padding: 0; }
        .sidebar-menu { list-style: none; padding: 1rem 0; margin: 0; }
        .sidebar-menu li { margin-bottom: 0.5rem; }
        .sidebar-menu a { display: block; padding: 0.8rem 1.5rem; color: #333; text-decoration: none; transition: all 0.3s ease; border-left: 3px solid transparent; }
        .sidebar-menu a:hover, .sidebar-menu a.active { background: rgba(102, 126, 234, 0.1); border-left-color: #667eea; color: #667eea; }
        .sidebar-menu i { width: 20px; margin-right: 10px; }
        .main-content { padding: 2rem; }
        .page-header { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .qualification-card { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .category-tabs { display: flex; gap: 1rem; margin-bottom: 2rem; }
        .category-tab { padding: 1rem 1.5rem; border-radius: 10px; background: #f8f9fa; color: #6c757d; text-decoration: none; transition: all 0.3s ease; border: 2px solid transparent; }
        .category-tab.active { background: rgba(102, 126, 234, 0.1); color: #667eea; border-color: #667eea; }
        .category-tab:hover { background: rgba(102, 126, 234, 0.05); color: #667eea; }
        .btn-primary { background: linear-gradient(45deg, #667eea, #764ba2); border: none; border-radius: 8px; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3); }
        .status-badge { padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.8rem; font-weight: 500; }
        .status-valid { background: rgba(40, 167, 69, 0.1); color: #28a745; }
        .status-invalid { background: rgba(220, 53, 69, 0.1); color: #dc3545; }
        .status-pending { background: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .status-reviewing { background: rgba(23, 162, 184, 0.1); color: #17a2b8; }
        .qualification-type { padding: 0.5rem 1rem; border-radius: 25px; font-size: 0.9rem; font-weight: 500; margin-right: 0.5rem; }
        .type-inspection { background: linear-gradient(45deg, #667eea, #764ba2); color: white; }
        .type-maintenance { background: linear-gradient(45deg, #28a745, #20c997); color: white; }
        .type-personnel { background: linear-gradient(45deg, #ffc107, #fd7e14); color: white; }
        .type-management { background: linear-gradient(45deg, #dc3545, #e83e8c); color: white; }
        .workflow-progress { display: flex; align-items: center; gap: 1rem; margin: 1rem 0; }
        .progress-step { width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; background: #e9ecef; color: #6c757d; font-size: 0.8rem; }
        .progress-step.active { background: #667eea; color: white; }
        .progress-step.completed { background: #28a745; color: white; }
        .progress-line { flex: 1; height: 2px; background: #e9ecef; }
        .progress-line.completed { background: #28a745; }
        .progress-line.active { background: #667eea; }
        .expert-review { background: #f8f9fa; border-radius: 10px; padding: 1rem; margin: 1rem 0; border-left: 4px solid #667eea; }
        .review-header { display: flex; justify-content: between; align-items: center; margin-bottom: 0.5rem; }
        .expert-name { font-weight: 600; color: #667eea; }
        .review-time { color: #6c757d; font-size: 0.9rem; }
        .review-content { color: #333; }
        .review-score { display: inline-block; padding: 0.2rem 0.5rem; border-radius: 15px; background: #28a745; color: white; font-size: 0.8rem; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.html"><i class="fas fa-building"></i> 幕墙信息化平台</a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 协会管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="membership.html"><i class="fas fa-users"></i> 入会管理</a></li>
                        <li><a href="qualification.html" class="active"><i class="fas fa-certificate"></i> 资质认定</a></li>
                        <li><a href="templates.html"><i class="fas fa-file-alt"></i> 表单模板</a></li>
                        <li><a href="training.html"><i class="fas fa-graduation-cap"></i> 培训管理</a></li>
                        <li><a href="workflow.html"><i class="fas fa-project-diagram"></i> 流程管理</a></li>
                        <li><a href="../company/list.html"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="../statistics/overview.html"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="../system/users.html"><i class="fas fa-cog"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2><i class="fas fa-certificate"></i> 资质认定管理</h2>
                                <p class="text-muted mb-0">管理各类资质认定申请、审核流程和认定结果</p>
                            </div>
                            <div>
                                <button class="btn btn-outline-primary me-2" onclick="exportQualificationData()">
                                    <i class="fas fa-download"></i> 导出数据
                                </button>
                                <button class="btn btn-primary" onclick="addQualificationStandard()">
                                    <i class="fas fa-plus"></i> 新增认定标准
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 资质认定分组 -->
                    <div class="qualification-card">
                        <h5><i class="fas fa-layer-group"></i> 资质认定分组</h5>
                        <div class="category-tabs">
                            <a href="#" class="category-tab active" onclick="switchCategory('inspection')">
                                <i class="fas fa-search"></i><br>
                                <strong>检查组申请认定</strong><br>
                                <small>23个申请</small>
                            </a>
                            <a href="#" class="category-tab" onclick="switchCategory('personnel')">
                                <i class="fas fa-user"></i><br>
                                <strong>检查组人员申请</strong><br>
                                <small>45个申请</small>
                            </a>
                            <a href="#" class="category-tab" onclick="switchCategory('maintenance')">
                                <i class="fas fa-tools"></i><br>
                                <strong>维修企业认定申请</strong><br>
                                <small>18个申请</small>
                            </a>
                            <a href="#" class="category-tab" onclick="switchCategory('manager')">
                                <i class="fas fa-user-tie"></i><br>
                                <strong>维修管理人员申请</strong><br>
                                <small>67个申请</small>
                            </a>
                            <a href="#" class="category-tab" onclick="switchCategory('worker')">
                                <i class="fas fa-hard-hat"></i><br>
                                <strong>维修施工人员申请</strong><br>
                                <small>89个申请</small>
                            </a>
                        </div>
                    </div>

                    <!-- 资质认定申请列表 -->
                    <div class="qualification-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5><i class="fas fa-list"></i> 资质认定申请列表</h5>
                            <div>
                                <select class="form-select d-inline-block w-auto me-2">
                                    <option>全部状态</option>
                                    <option>待审核</option>
                                    <option>审核中</option>
                                    <option>认定有效</option>
                                    <option>认定失效</option>
                                </select>
                                <input type="text" class="form-control d-inline-block w-auto me-2" placeholder="搜索企业名称">
                                <button class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>企业/人员名称</th>
                                        <th>认定资质</th>
                                        <th>申请时间</th>
                                        <th>当前状态</th>
                                        <th>有效期</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>上海某幕墙检测有限公司</strong>
                                                <br><small class="text-muted">统一社会信用代码：91310000123456789X</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="qualification-type type-inspection">幕墙检查组</span>
                                        </td>
                                        <td>2024-02-10</td>
                                        <td><span class="status-badge status-valid">认定有效</span></td>
                                        <td>2026-02-10</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" onclick="viewQualificationDetail(1)">
                                                <i class="fas fa-eye"></i> 详情
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>上海某维修工程有限公司</strong>
                                                <br><small class="text-muted">统一社会信用代码：91310000987654321Y</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="qualification-type type-maintenance">维修企业</span>
                                        </td>
                                        <td>2024-02-15</td>
                                        <td><span class="status-badge status-reviewing">专家评审中</span></td>
                                        <td>-</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" onclick="viewQualificationDetail(2)">
                                                <i class="fas fa-eye"></i> 详情
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>张三</strong>
                                                <br><small class="text-muted">身份证：310101199001011234</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="qualification-type type-management">维修管理人员</span>
                                        </td>
                                        <td>2024-02-18</td>
                                        <td><span class="status-badge status-pending">材料审核中</span></td>
                                        <td>-</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" onclick="viewQualificationDetail(3)">
                                                <i class="fas fa-eye"></i> 详情
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>上海某建筑装饰有限公司</strong>
                                                <br><small class="text-muted">统一社会信用代码：91310000456789123Z</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="qualification-type type-maintenance">维修企业</span>
                                        </td>
                                        <td>2023-08-20</td>
                                        <td><span class="status-badge status-invalid">认定失效</span></td>
                                        <td>2024-08-20（已过期）</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-warning" onclick="renewQualification(4)">
                                                <i class="fas fa-redo"></i> 续期申请
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <nav aria-label="资质认定列表分页">
                            <ul class="pagination justify-content-center">
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1">上一页</a>
                                </li>
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                <li class="page-item"><a class="page-link" href="#">3</a></li>
                                <li class="page-item">
                                    <a class="page-link" href="#">下一页</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 资质认定详情模态框 -->
    <div class="modal fade" id="qualificationModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-certificate"></i> 幕墙检查组认定详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 基本信息 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle"></i> 申请基本信息</h6>
                            <table class="table table-sm">
                                <tr><td>企业名称</td><td>上海某幕墙检测有限公司</td></tr>
                                <tr><td>申请资质</td><td>幕墙检查组认定</td></tr>
                                <tr><td>申请时间</td><td>2024-02-10</td></tr>
                                <tr><td>申请人</td><td>李经理</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-building"></i> 企业基本情况</h6>
                            <table class="table table-sm">
                                <tr><td>注册资本</td><td>5000万元</td></tr>
                                <tr><td>成立时间</td><td>2018-03-15</td></tr>
                                <tr><td>员工人数</td><td>85人</td></tr>
                                <tr><td>技术人员</td><td>32人</td></tr>
                            </table>
                        </div>
                    </div>

                    <!-- 工作流程进度 -->
                    <div class="mb-4">
                        <h6><i class="fas fa-tasks"></i> 认定流程进度</h6>
                        <div class="workflow-progress">
                            <div class="progress-step completed">1</div>
                            <div class="progress-line completed"></div>
                            <div class="progress-step completed">2</div>
                            <div class="progress-line active"></div>
                            <div class="progress-step active">3</div>
                            <div class="progress-line"></div>
                            <div class="progress-step">4</div>
                        </div>
                        <div class="d-flex justify-content-between text-sm">
                            <span class="text-success">条件审核</span>
                            <span class="text-success">材料复核</span>
                            <span class="text-primary">专家评审</span>
                            <span class="text-muted">最终审批</span>
                        </div>
                    </div>

                    <!-- 专家评审意见 -->
                    <div class="mb-4">
                        <h6><i class="fas fa-users"></i> 专家评审意见</h6>
                        
                        <div class="expert-review">
                            <div class="review-header">
                                <div>
                                    <span class="expert-name">王教授</span>
                                    <span class="review-score">85分</span>
                                </div>
                                <span class="review-time">2024-02-18 14:30</span>
                            </div>
                            <div class="review-content">
                                该企业具备较强的技术实力，检测设备齐全，人员配置合理。建议通过认定，但需要加强质量管理体系建设。
                            </div>
                        </div>

                        <div class="expert-review">
                            <div class="review-header">
                                <div>
                                    <span class="expert-name">李高工</span>
                                    <span class="review-score">90分</span>
                                </div>
                                <span class="review-time">2024-02-19 09:15</span>
                            </div>
                            <div class="review-content">
                                企业资质完备，技术人员经验丰富，检测报告规范。现场核查情况良好，推荐通过认定。
                            </div>
                        </div>

                        <div class="expert-review">
                            <div class="review-header">
                                <div>
                                    <span class="expert-name">张工程师</span>
                                    <span class="review-score">待评审</span>
                                </div>
                                <span class="review-time">待完成</span>
                            </div>
                            <div class="review-content text-muted">
                                等待专家评审...
                            </div>
                        </div>
                    </div>

                    <!-- 申请材料 -->
                    <div class="mb-4">
                        <h6><i class="fas fa-folder"></i> 申请材料</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        营业执照
                                        <span class="badge bg-success rounded-pill">已提交</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        资质证书
                                        <span class="badge bg-success rounded-pill">已提交</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        人员名单
                                        <span class="badge bg-success rounded-pill">已提交</span>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        检测设备清单
                                        <span class="badge bg-success rounded-pill">已提交</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        质量管理体系
                                        <span class="badge bg-warning rounded-pill">待补充</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        业绩证明材料
                                        <span class="badge bg-success rounded-pill">已提交</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-warning me-2" onclick="requestSupplementary()">
                        <i class="fas fa-exclamation-triangle"></i> 要求补充材料
                    </button>
                    <button type="button" class="btn btn-danger me-2" onclick="rejectApplication()">
                        <i class="fas fa-times"></i> 驳回申请
                    </button>
                    <button type="button" class="btn btn-success" onclick="approveApplication()">
                        <i class="fas fa-check"></i> 通过认定
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 切换资质分组
        function switchCategory(category) {
            document.querySelectorAll('.category-tab').forEach(tab => tab.classList.remove('active'));
            event.target.closest('.category-tab').classList.add('active');
            
            // 这里可以根据分组加载不同的数据
            console.log('切换到分组:', category);
        }

        // 查看资质认定详情
        function viewQualificationDetail(id) {
            const modal = new bootstrap.Modal(document.getElementById('qualificationModal'));
            modal.show();
        }

        // 续期申请
        function renewQualification(id) {
            if (confirm('确认为该企业发起续期申请？')) {
                alert('续期申请已创建！');
            }
        }

        // 要求补充材料
        function requestSupplementary() {
            alert('补充材料通知已发送！');
        }

        // 驳回申请
        function rejectApplication() {
            if (confirm('确认驳回该认定申请？')) {
                alert('申请已驳回！');
                bootstrap.Modal.getInstance(document.getElementById('qualificationModal')).hide();
            }
        }

        // 通过认定
        function approveApplication() {
            if (confirm('确认通过该认定申请？')) {
                alert('认定申请已通过！');
                bootstrap.Modal.getInstance(document.getElementById('qualificationModal')).hide();
                location.reload();
            }
        }

        // 导出数据
        function exportQualificationData() {
            alert('正在导出资质认定数据...');
        }

        // 新增认定标准
        function addQualificationStandard() {
            alert('新增认定标准功能开发中...');
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.qualification-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
