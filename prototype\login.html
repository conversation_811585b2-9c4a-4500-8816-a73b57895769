<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            margin: 2rem;
        }
        .login-left {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        .login-right {
            padding: 3rem;
        }
        .logo {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .platform-title {
            font-size: 1.8rem;
            font-weight: 300;
            margin-bottom: 1rem;
        }
        .platform-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        .feature-list {
            list-style: none;
            padding: 0;
            text-align: left;
        }
        .feature-list li {
            margin-bottom: 0.8rem;
            padding-left: 1.5rem;
            position: relative;
        }
        .feature-list li:before {
            content: '\f00c';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: #4CAF50;
        }
        .login-form {
            max-width: 400px;
            margin: 0 auto;
        }
        .form-title {
            text-align: center;
            margin-bottom: 2rem;
            color: #333;
            font-size: 1.8rem;
            font-weight: 600;
        }
        .form-floating {
            margin-bottom: 1rem;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 1rem;
            font-weight: 600;
            width: 100%;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        .captcha-container {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        .captcha-image {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 0.5rem;
            cursor: pointer;
            font-family: monospace;
            font-size: 1.2rem;
            font-weight: bold;
            color: #667eea;
            text-align: center;
            min-width: 100px;
            user-select: none;
        }
        .form-check {
            margin-bottom: 1rem;
        }
        .forgot-password {
            text-align: center;
            margin-top: 1rem;
        }
        .forgot-password a {
            color: #667eea;
            text-decoration: none;
        }
        .forgot-password a:hover {
            text-decoration: underline;
        }
        .register-link {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }
        .register-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }
        .register-link a:hover {
            text-decoration: underline;
        }
        .back-home {
            position: absolute;
            top: 2rem;
            left: 2rem;
            color: white;
            font-size: 1.2rem;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .back-home:hover {
            color: rgba(255, 255, 255, 0.8);
            transform: translateX(-5px);
        }
        @media (max-width: 768px) {
            .login-left {
                display: none;
            }
            .login-container {
                margin: 1rem;
            }
            .login-right {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <a href="index.html" class="back-home">
        <i class="fas fa-arrow-left"></i> 返回首页
    </a>

    <div class="login-container">
        <div class="row g-0">
            <div class="col-lg-6 d-none d-lg-block">
                <div class="login-left">
                    <div class="logo">
                        <i class="fas fa-building"></i>
                    </div>
                    <h2 class="platform-title">幕墙信息化平台</h2>
                    <p class="platform-subtitle">上海市装饰装修行业协会</p>
                    
                    <ul class="feature-list">
                        <li>企业信息统一管理</li>
                        <li>项目进度实时跟踪</li>
                        <li>既有幕墙安全监管</li>
                        <li>行业数据统计分析</li>
                        <li>协会服务便民高效</li>
                    </ul>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="login-right">
                    <form class="login-form" id="loginForm">
                        <h3 class="form-title">
                            <i class="fas fa-sign-in-alt"></i> 用户登录
                        </h3>
                        
                        <div class="form-floating">
                            <input type="text" class="form-control" id="username" placeholder="用户名" required>
                            <label for="username">
                                <i class="fas fa-user"></i> 用户名/手机号
                            </label>
                        </div>
                        
                        <div class="form-floating">
                            <input type="password" class="form-control" id="password" placeholder="密码" required>
                            <label for="password">
                                <i class="fas fa-lock"></i> 密码
                            </label>
                        </div>
                        
                        <div class="captcha-container">
                            <div class="form-floating flex-grow-1">
                                <input type="text" class="form-control" id="captcha" placeholder="验证码" required>
                                <label for="captcha">
                                    <i class="fas fa-shield-alt"></i> 验证码
                                </label>
                            </div>
                            <div class="captcha-image" id="captchaImage" onclick="refreshCaptcha()">
                                A8K9
                            </div>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                记住密码
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-login">
                            <i class="fas fa-sign-in-alt"></i> 登录
                        </button>
                        
                        <div class="forgot-password">
                            <a href="#" onclick="showForgotPassword()">
                                <i class="fas fa-question-circle"></i> 忘记密码？
                            </a>
                        </div>
                        
                        <div class="register-link">
                            <p>还没有账号？ <a href="register.html">立即注册</a></p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 忘记密码模态框 -->
    <div class="modal fade" id="forgotPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-key"></i> 找回密码
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="forgotPasswordForm">
                        <div class="mb-3">
                            <label for="resetPhone" class="form-label">手机号码</label>
                            <input type="tel" class="form-control" id="resetPhone" placeholder="请输入注册时的手机号" required>
                        </div>
                        <div class="row">
                            <div class="col-8">
                                <label for="smsCode" class="form-label">短信验证码</label>
                                <input type="text" class="form-control" id="smsCode" placeholder="请输入验证码" required>
                            </div>
                            <div class="col-4">
                                <label class="form-label">&nbsp;</label>
                                <button type="button" class="btn btn-outline-primary w-100" id="sendSmsBtn">
                                    发送验证码
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="resetPassword()">确认重置</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 验证码生成
        function generateCaptcha() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let result = '';
            for (let i = 0; i < 4; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }

        // 刷新验证码
        function refreshCaptcha() {
            document.getElementById('captchaImage').textContent = generateCaptcha();
        }

        // 登录表单提交
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const captcha = document.getElementById('captcha').value;
            const captchaImage = document.getElementById('captchaImage').textContent;
            
            // 验证码检查
            if (captcha.toUpperCase() !== captchaImage) {
                alert('验证码错误，请重新输入！');
                refreshCaptcha();
                document.getElementById('captcha').value = '';
                return;
            }
            
            // 模拟登录验证
            if (username === 'admin' && password === '123456') {
                alert('登录成功！即将跳转到系统首页...');
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1000);
            } else if (username === 'company' && password === '123456') {
                alert('企业用户登录成功！');
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1000);
            } else {
                alert('用户名或密码错误！\n\n演示账号：\n管理员：admin/123456\n企业用户：company/123456');
                refreshCaptcha();
            }
        });

        // 显示忘记密码模态框
        function showForgotPassword() {
            const modal = new bootstrap.Modal(document.getElementById('forgotPasswordModal'));
            modal.show();
        }

        // 发送短信验证码
        let smsCountdown = 0;
        document.getElementById('sendSmsBtn').addEventListener('click', function() {
            const phone = document.getElementById('resetPhone').value;
            if (!phone) {
                alert('请输入手机号码！');
                return;
            }
            
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                alert('请输入正确的手机号码！');
                return;
            }
            
            // 模拟发送验证码
            alert('验证码已发送到您的手机，请注意查收！');
            
            // 倒计时
            smsCountdown = 60;
            const btn = this;
            btn.disabled = true;
            const timer = setInterval(() => {
                btn.textContent = `${smsCountdown}秒后重发`;
                smsCountdown--;
                if (smsCountdown < 0) {
                    clearInterval(timer);
                    btn.disabled = false;
                    btn.textContent = '发送验证码';
                }
            }, 1000);
        });

        // 重置密码
        function resetPassword() {
            const phone = document.getElementById('resetPhone').value;
            const smsCode = document.getElementById('smsCode').value;
            
            if (!phone || !smsCode) {
                alert('请填写完整信息！');
                return;
            }
            
            // 模拟重置密码
            alert('密码重置成功！新密码已发送到您的手机，请注意查收！');
            bootstrap.Modal.getInstance(document.getElementById('forgotPasswordModal')).hide();
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.login-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.6s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });

        // 初始化验证码
        refreshCaptcha();
    </script>
</body>
</html>
