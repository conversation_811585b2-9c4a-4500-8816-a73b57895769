# 技术架构图

```mermaid
graph TB
    subgraph "前端技术栈"
        VUE[Vue.js 3.x]
        ELEMENT[Element Plus UI]
        AXIOS[Axios HTTP客户端]
        ROUTER[Vue Router]
        VUEX[Vuex/Pinia 状态管理]
        VITE[Vite 构建工具]
    end

    subgraph "后端技术栈"
        SPRING[Spring Boot 2.7.x]
        SECURITY[Spring Security]
        JPA[Spring Data JPA]
        REDIS_CLIENT[Spring Data Redis]
        MYBATIS[MyBatis Plus]
        VALIDATION[Spring Validation]
    end

    subgraph "数据库技术"
        MYSQL[MySQL 8.0]
        REDIS[Redis 6.x]
        ES[Elasticsearch 7.x]
        MINIO[MinIO 对象存储]
    end

    subgraph "中间件技术"
        NGINX[Nginx 反向代理]
        RABBITMQ[RabbitMQ 消息队列]
        NACOS[Nacos 服务发现]
        GATEWAY[Spring Cloud Gateway]
    end

    subgraph "监控运维技术"
        PROMETHEUS[Prometheus 监控]
        GRAFANA[Grafana 可视化]
        ELK[ELK 日志分析]
        DOCKER[Docker 容器化]
        K8S[Kubernetes 编排]
    end

    subgraph "开发工具链"
        MAVEN[Maven 依赖管理]
        GIT[Git 版本控制]
        JENKINS[Jenkins CI/CD]
        SONAR[SonarQube 代码质量]
        SWAGGER[Swagger API文档]
    end

    subgraph "安全技术"
        JWT[JWT Token]
        OAUTH2[OAuth2.0]
        SSL[SSL/TLS 加密]
        WAF_TECH[ModSecurity WAF]
    end

    %% 前端技术关系
    VUE --> ELEMENT
    VUE --> ROUTER
    VUE --> VUEX
    VUE --> AXIOS
    VITE --> VUE

    %% 后端技术关系
    SPRING --> SECURITY
    SPRING --> JPA
    SPRING --> REDIS_CLIENT
    SPRING --> MYBATIS
    SPRING --> VALIDATION

    %% 前后端交互
    AXIOS --> GATEWAY
    GATEWAY --> SPRING

    %% 数据库连接
    JPA --> MYSQL
    MYBATIS --> MYSQL
    REDIS_CLIENT --> REDIS
    SPRING --> ES
    SPRING --> MINIO

    %% 中间件集成
    NGINX --> VUE
    NGINX --> GATEWAY
    SPRING --> RABBITMQ
    GATEWAY --> NACOS
    SPRING --> NACOS

    %% 安全集成
    SECURITY --> JWT
    SECURITY --> OAUTH2
    NGINX --> SSL
    NGINX --> WAF_TECH

    %% 监控集成
    SPRING --> PROMETHEUS
    PROMETHEUS --> GRAFANA
    SPRING --> ELK
    DOCKER --> K8S

    %% 开发工具集成
    SPRING --> MAVEN
    VUE --> GIT
    SPRING --> GIT
    JENKINS --> GIT
    JENKINS --> DOCKER
    SPRING --> SWAGGER
    SPRING --> SONAR

    classDef frontendTech fill:#e3f2fd
    classDef backendTech fill:#e8f5e8
    classDef dataTech fill:#fff3e0
    classDef middlewareTech fill:#f3e5f5
    classDef monitorTech fill:#fce4ec
    classDef devTech fill:#f1f8e9
    classDef securityTech fill:#ffebee

    class VUE,ELEMENT,AXIOS,ROUTER,VUEX,VITE frontendTech
    class SPRING,SECURITY,JPA,REDIS_CLIENT,MYBATIS,VALIDATION backendTech
    class MYSQL,REDIS,ES,MINIO dataTech
    class NGINX,RABBITMQ,NACOS,GATEWAY middlewareTech
    class PROMETHEUS,GRAFANA,ELK,DOCKER,K8S monitorTech
    class MAVEN,GIT,JENKINS,SONAR,SWAGGER devTech
    class JWT,OAUTH2,SSL,WAF_TECH securityTech
```

## 技术选型说明

### 1. 前端技术栈

#### Vue.js 3.x
- **选择理由**: 渐进式框架，学习成本低，生态完善
- **版本**: 3.x（Composition API支持）
- **特性**: 响应式数据绑定，组件化开发

#### Element Plus UI
- **选择理由**: 基于Vue 3的企业级UI组件库
- **特性**: 丰富的组件，一致的设计语言
- **适用场景**: 后台管理系统界面

#### Axios
- **选择理由**: 基于Promise的HTTP客户端
- **特性**: 请求/响应拦截器，自动JSON数据转换
- **用途**: 前后端数据交互

#### Vue Router
- **选择理由**: Vue.js官方路由管理器
- **特性**: 嵌套路由，路由守卫，懒加载
- **用途**: 单页应用路由管理

#### Vuex/Pinia
- **选择理由**: Vue.js状态管理模式
- **特性**: 集中式状态管理，时间旅行调试
- **用途**: 全局状态管理

#### Vite
- **选择理由**: 下一代前端构建工具
- **特性**: 快速冷启动，热模块替换
- **用途**: 开发环境构建和生产打包

### 2. 后端技术栈

#### Spring Boot 2.7.x
- **选择理由**: 企业级Java开发框架
- **特性**: 自动配置，内嵌服务器，生产就绪
- **版本**: 2.7.x（稳定版本）

#### Spring Security
- **选择理由**: 强大的安全框架
- **特性**: 认证授权，CSRF防护，会话管理
- **用途**: 系统安全控制

#### Spring Data JPA
- **选择理由**: 简化数据访问层开发
- **特性**: 自动生成CRUD操作，查询方法
- **用途**: 数据库操作抽象

#### MyBatis Plus
- **选择理由**: MyBatis增强工具
- **特性**: 代码生成器，条件构造器，分页插件
- **用途**: 复杂SQL查询和数据操作

#### Spring Data Redis
- **选择理由**: Redis集成支持
- **特性**: 模板操作，序列化支持
- **用途**: 缓存和会话存储

### 3. 数据库技术

#### MySQL 8.0
- **选择理由**: 成熟稳定的关系型数据库
- **特性**: ACID事务，JSON支持，性能优化
- **用途**: 主要业务数据存储

#### Redis 6.x
- **选择理由**: 高性能内存数据库
- **特性**: 多种数据结构，持久化，集群支持
- **用途**: 缓存和会话存储

#### Elasticsearch 7.x
- **选择理由**: 分布式搜索引擎
- **特性**: 全文搜索，实时分析，RESTful API
- **用途**: 复杂查询和数据分析

#### MinIO
- **选择理由**: 高性能对象存储
- **特性**: S3兼容API，分布式架构
- **用途**: 文件和图片存储

### 4. 中间件技术

#### Nginx
- **选择理由**: 高性能Web服务器
- **特性**: 反向代理，负载均衡，静态文件服务
- **用途**: 前端代理和负载均衡

#### RabbitMQ
- **选择理由**: 可靠的消息队列
- **特性**: 消息持久化，路由机制，集群支持
- **用途**: 异步处理和系统解耦

#### Nacos
- **选择理由**: 动态服务发现和配置管理
- **特性**: 服务注册发现，配置中心，健康检查
- **用途**: 微服务治理

#### Spring Cloud Gateway
- **选择理由**: 基于Spring的API网关
- **特性**: 路由转发，过滤器链，限流熔断
- **用途**: 统一API入口

### 5. 监控运维技术

#### Prometheus + Grafana
- **选择理由**: 现代化监控解决方案
- **特性**: 时序数据库，丰富的可视化
- **用途**: 系统监控和告警

#### ELK Stack
- **选择理由**: 完整的日志分析解决方案
- **组件**: Elasticsearch + Logstash + Kibana
- **用途**: 日志收集、分析和可视化

#### Docker + Kubernetes
- **选择理由**: 容器化部署和编排
- **特性**: 环境一致性，弹性伸缩，服务发现
- **用途**: 应用部署和运维管理

### 6. 开发工具链

#### Maven
- **选择理由**: Java项目依赖管理
- **特性**: 依赖管理，构建生命周期，插件生态
- **用途**: 项目构建和依赖管理

#### Git
- **选择理由**: 分布式版本控制
- **特性**: 分支管理，合并策略，分布式协作
- **用途**: 代码版本管理

#### Jenkins
- **选择理由**: 持续集成/持续部署
- **特性**: 流水线，插件生态，自动化部署
- **用途**: CI/CD自动化

#### SonarQube
- **选择理由**: 代码质量管理
- **特性**: 代码扫描，质量门禁，技术债务分析
- **用途**: 代码质量保证

#### Swagger
- **选择理由**: API文档生成
- **特性**: 自动生成文档，在线测试，代码生成
- **用途**: API文档和测试

### 7. 安全技术

#### JWT Token
- **选择理由**: 无状态认证方案
- **特性**: 自包含，跨域支持，无状态
- **用途**: 用户认证和授权

#### OAuth2.0
- **选择理由**: 标准授权协议
- **特性**: 第三方授权，权限范围控制
- **用途**: 第三方系统集成

#### SSL/TLS
- **选择理由**: 传输层安全协议
- **特性**: 数据加密，身份验证，数据完整性
- **用途**: HTTPS安全传输

#### ModSecurity WAF
- **选择理由**: Web应用防火墙
- **特性**: SQL注入防护，XSS防护，DDoS防护
- **用途**: Web应用安全防护

## 技术架构优势

1. **成熟稳定**: 选择经过生产验证的技术栈
2. **性能优异**: 缓存、异步处理、负载均衡等性能优化
3. **安全可靠**: 多层安全防护，数据加密传输
4. **易于维护**: 标准化技术栈，丰富的社区支持
5. **可扩展性**: 微服务架构，支持水平扩展
