# 部署和运维文档

## 1. 系统环境要求

### 1.1 硬件要求
#### 1.1.1 最小配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 100GB SSD
- **网络**: 100Mbps带宽

#### 1.1.2 推荐配置
- **CPU**: 8核心
- **内存**: 16GB RAM
- **存储**: 500GB SSD
- **网络**: 1Gbps带宽

#### 1.1.3 生产环境配置
- **CPU**: 16核心
- **内存**: 32GB RAM
- **存储**: 1TB SSD + 2TB HDD（备份）
- **网络**: 10Gbps带宽

### 1.2 软件要求
#### 1.2.1 操作系统
- **推荐**: CentOS 7.x / Ubuntu 18.04 LTS 或更高版本
- **支持**: Windows Server 2016 或更高版本

#### 1.2.2 基础软件
- **Java**: OpenJDK 11 或 Oracle JDK 11
- **Node.js**: 16.x 或更高版本
- **MySQL**: 8.0 或更高版本
- **Redis**: 6.x 或更高版本
- **Nginx**: 1.18 或更高版本
- **Docker**: 20.10 或更高版本（可选）

## 2. 部署架构

### 2.1 单机部署架构
```
┌─────────────────────────────────────────┐
│              服务器                      │
│  ┌─────────┐  ┌─────────┐  ┌─────────┐  │
│  │  Nginx  │  │  应用   │  │  MySQL  │  │
│  │  (80)   │  │ (8080)  │  │ (3306)  │  │
│  └─────────┘  └─────────┘  └─────────┘  │
│  ┌─────────┐  ┌─────────┐  ┌─────────┐  │
│  │  Redis  │  │  ES     │  │  MinIO  │  │
│  │ (6379)  │  │ (9200)  │  │ (9000)  │  │
│  └─────────┘  └─────────┘  └─────────┘  │
└─────────────────────────────────────────┘
```

### 2.2 集群部署架构
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   负载均衡   │    │   应用服务   │    │   数据服务   │
│             │    │             │    │             │
│  ┌─────────┐│    │ ┌─────────┐ │    │ ┌─────────┐ │
│  │  Nginx  ││    │ │  App1   │ │    │ │ MySQL   │ │
│  │   LB    ││    │ │ (8080)  │ │    │ │ Master  │ │
│  └─────────┘│    │ └─────────┘ │    │ └─────────┘ │
│             │    │ ┌─────────┐ │    │ ┌─────────┐ │
│             │    │ │  App2   │ │    │ │ MySQL   │ │
│             │    │ │ (8081)  │ │    │ │ Slave   │ │
│             │    │ └─────────┘ │    │ └─────────┘ │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       └───────────────────┼───────────────────┘
                           │
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   缓存服务   │    │   搜索服务   │    │   文件服务   │
│             │    │             │    │             │
│ ┌─────────┐ │    │ ┌─────────┐ │    │ ┌─────────┐ │
│ │ Redis   │ │    │ │   ES    │ │    │ │  MinIO  │ │
│ │Cluster  │ │    │ │Cluster  │ │    │ │Cluster  │ │
│ └─────────┘ │    │ └─────────┘ │    │ └─────────┘ │
└─────────────┘    └─────────────┘    └─────────────┘
```

## 3. 安装部署步骤

### 3.1 环境准备
#### 3.1.1 安装Java环境
```bash
# CentOS/RHEL
sudo yum install -y java-11-openjdk java-11-openjdk-devel

# Ubuntu/Debian
sudo apt update
sudo apt install -y openjdk-11-jdk

# 验证安装
java -version
```

#### 3.1.2 安装Node.js环境
```bash
# 使用NodeSource仓库安装
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

#### 3.1.3 安装MySQL数据库
```bash
# 下载MySQL APT仓库
wget https://dev.mysql.com/get/mysql-apt-config_0.8.22-1_all.deb
sudo dpkg -i mysql-apt-config_0.8.22-1_all.deb

# 安装MySQL服务器
sudo apt update
sudo apt install -y mysql-server

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

#### 3.1.4 安装Redis缓存
```bash
# 安装Redis
sudo apt install -y redis-server

# 配置Redis
sudo vim /etc/redis/redis.conf
# 修改以下配置：
# bind 127.0.0.1
# requirepass your_password

# 启动Redis服务
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

#### 3.1.5 安装Nginx
```bash
# 安装Nginx
sudo apt install -y nginx

# 启动Nginx服务
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 3.2 数据库初始化
#### 3.2.1 创建数据库
```sql
-- 连接MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE curtain_wall_platform DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'cwp_user'@'%' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON curtain_wall_platform.* TO 'cwp_user'@'%';
FLUSH PRIVILEGES;
```

#### 3.2.2 导入数据库结构
```bash
# 导入数据库结构
mysql -u cwp_user -p curtain_wall_platform < database/schema.sql

# 导入初始数据
mysql -u cwp_user -p curtain_wall_platform < database/init_data.sql
```

### 3.3 应用部署
#### 3.3.1 后端应用部署
```bash
# 创建应用目录
sudo mkdir -p /opt/curtain-wall-platform
cd /opt/curtain-wall-platform

# 上传应用包
# 将 curtain-wall-platform-backend.jar 上传到服务器

# 创建配置文件
sudo vim application-prod.yml
```

**application-prod.yml 配置示例：**
```yaml
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  datasource:
    url: **********************************************************************************************************************************
    username: cwp_user
    password: strong_password
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  redis:
    host: localhost
    port: 6379
    password: redis_password
    database: 0
    
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    
logging:
  level:
    com.curtainwall: INFO
  file:
    name: /var/log/curtain-wall-platform/application.log
```

#### 3.3.2 创建系统服务
```bash
# 创建服务文件
sudo vim /etc/systemd/system/curtain-wall-platform.service
```

**服务配置文件：**
```ini
[Unit]
Description=Curtain Wall Platform Backend Service
After=network.target

[Service]
Type=simple
User=cwp
Group=cwp
WorkingDirectory=/opt/curtain-wall-platform
ExecStart=/usr/bin/java -jar -Xms2g -Xmx4g -Dspring.profiles.active=prod curtain-wall-platform-backend.jar
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### 3.3.3 启动后端服务
```bash
# 创建用户
sudo useradd -r -s /bin/false cwp
sudo chown -R cwp:cwp /opt/curtain-wall-platform

# 启动服务
sudo systemctl daemon-reload
sudo systemctl start curtain-wall-platform
sudo systemctl enable curtain-wall-platform

# 检查服务状态
sudo systemctl status curtain-wall-platform
```

#### 3.3.4 前端应用部署
```bash
# 构建前端应用
cd frontend
npm install
npm run build

# 部署到Nginx
sudo cp -r dist/* /var/www/html/
sudo chown -R www-data:www-data /var/www/html/
```

#### 3.3.5 配置Nginx
```bash
# 创建Nginx配置
sudo vim /etc/nginx/sites-available/curtain-wall-platform
```

**Nginx配置文件：**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /var/www/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 文件上传大小限制
    client_max_body_size 100M;
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/curtain-wall-platform /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 4. 监控配置

### 4.1 应用监控
#### 4.1.1 安装Prometheus
```bash
# 下载Prometheus
wget https://github.com/prometheus/prometheus/releases/download/v2.40.0/prometheus-2.40.0.linux-amd64.tar.gz
tar xvfz prometheus-2.40.0.linux-amd64.tar.gz
sudo mv prometheus-2.40.0.linux-amd64 /opt/prometheus

# 创建配置文件
sudo vim /opt/prometheus/prometheus.yml
```

#### 4.1.2 安装Grafana
```bash
# 添加Grafana仓库
sudo apt-get install -y software-properties-common
sudo add-apt-repository "deb https://packages.grafana.com/oss/deb stable main"
wget -q -O - https://packages.grafana.com/gpg.key | sudo apt-key add -

# 安装Grafana
sudo apt-get update
sudo apt-get install grafana

# 启动Grafana
sudo systemctl start grafana-server
sudo systemctl enable grafana-server
```

### 4.2 日志监控
#### 4.2.1 配置日志收集
```bash
# 安装Filebeat
wget https://artifacts.elastic.co/downloads/beats/filebeat/filebeat-8.5.0-amd64.deb
sudo dpkg -i filebeat-8.5.0-amd64.deb

# 配置Filebeat
sudo vim /etc/filebeat/filebeat.yml
```

## 5. 备份策略

### 5.1 数据库备份
#### 5.1.1 创建备份脚本
```bash
#!/bin/bash
# 数据库备份脚本

BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="curtain_wall_platform"
DB_USER="cwp_user"
DB_PASS="strong_password"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete

echo "Database backup completed: backup_$DATE.sql.gz"
```

#### 5.1.2 设置定时备份
```bash
# 添加到crontab
crontab -e

# 每天凌晨2点执行备份
0 2 * * * /opt/scripts/backup_database.sh
```

### 5.2 应用备份
```bash
#!/bin/bash
# 应用备份脚本

APP_DIR="/opt/curtain-wall-platform"
BACKUP_DIR="/backup/application"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份应用文件
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz -C $APP_DIR .

# 删除30天前的备份
find $BACKUP_DIR -name "app_backup_*.tar.gz" -mtime +30 -delete

echo "Application backup completed: app_backup_$DATE.tar.gz"
```

## 6. 安全配置

### 6.1 防火墙配置
```bash
# 安装ufw
sudo apt install ufw

# 配置防火墙规则
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 启用防火墙
sudo ufw enable
```

### 6.2 SSL证书配置
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

## 7. 故障排查

### 7.1 常见问题
#### 7.1.1 应用启动失败
```bash
# 检查日志
sudo journalctl -u curtain-wall-platform -f

# 检查端口占用
sudo netstat -tlnp | grep 8080

# 检查Java进程
ps aux | grep java
```

#### 7.1.2 数据库连接问题
```bash
# 检查MySQL状态
sudo systemctl status mysql

# 检查数据库连接
mysql -u cwp_user -p -h localhost

# 检查数据库配置
sudo vim /etc/mysql/mysql.conf.d/mysqld.cnf
```

#### 7.1.3 内存不足
```bash
# 检查内存使用
free -h
top

# 调整JVM参数
sudo vim /etc/systemd/system/curtain-wall-platform.service
# 修改 -Xms 和 -Xmx 参数
```

### 7.2 性能优化
#### 7.2.1 数据库优化
```sql
-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 分析查询性能
EXPLAIN SELECT * FROM company WHERE company_type = 1;

-- 添加索引
CREATE INDEX idx_company_type_status ON company(company_type, status);
```

#### 7.2.2 应用优化
```bash
# JVM调优参数
-Xms4g -Xmx8g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+HeapDumpOnOutOfMemoryError
```

## 8. 运维检查清单

### 8.1 日常检查
- [ ] 检查应用服务状态
- [ ] 检查数据库连接
- [ ] 检查磁盘空间使用
- [ ] 检查内存使用情况
- [ ] 检查系统负载
- [ ] 检查错误日志

### 8.2 周期检查
- [ ] 数据库备份验证
- [ ] 安全补丁更新
- [ ] 性能指标分析
- [ ] 容量规划评估
- [ ] 监控告警测试
