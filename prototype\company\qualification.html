<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业资质管理 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar {
            background: linear-gradient(45deg, #667eea, #764ba2);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 0;
        }
        .sidebar-menu {
            list-style: none;
            padding: 1rem 0;
            margin: 0;
        }
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        .sidebar-menu a {
            display: block;
            padding: 0.8rem 1.5rem;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background: rgba(102, 126, 234, 0.1);
            border-left-color: #667eea;
            color: #667eea;
        }
        .sidebar-menu i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            padding: 2rem;
        }
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .company-info {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .qualification-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .qualification-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }
        .qualification-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .qualification-header {
            display: flex;
            justify-content-between;
            align-items: start;
            margin-bottom: 1rem;
        }
        .qualification-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }
        .qualification-code {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        .qualification-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        .detail-item {
            display: flex;
            flex-direction: column;
        }
        .detail-label {
            font-weight: 600;
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.3rem;
        }
        .detail-value {
            color: #333;
        }
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .status-valid { background: rgba(40, 167, 69, 0.1); color: #28a745; }
        .status-expiring { background: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .status-expired { background: rgba(220, 53, 69, 0.1); color: #dc3545; }
        .status-pending { background: rgba(108, 117, 125, 0.1); color: #6c757d; }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 8px;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .action-buttons .btn {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #ddd;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -250px;
                width: 250px;
                z-index: 1000;
                transition: left 0.3s ease;
            }
            .sidebar.show {
                left: 0;
            }
            .main-content {
                padding: 1rem;
            }
            .qualification-header {
                flex-direction: column;
                align-items: start;
            }
            .qualification-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <button class="navbar-toggler d-lg-none" type="button" onclick="toggleSidebar()">
                <span class="navbar-toggler-icon"></span>
            </button>
            <a class="navbar-brand" href="../dashboard.html">
                <i class="fas fa-building"></i> 幕墙信息化平台
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user"></i> 个人资料</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-cog"></i> 系统设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar" id="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="list.html" class="active"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="../project/list.html"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="../curtain-wall/list.html"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="../statistics/overview.html"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="../system/users.html"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb mb-2">
                                        <li class="breadcrumb-item"><a href="list.html">企业管理</a></li>
                                        <li class="breadcrumb-item"><a href="detail.html?id=1">企业详情</a></li>
                                        <li class="breadcrumb-item active">资质管理</li>
                                    </ol>
                                </nav>
                                <h2><i class="fas fa-certificate"></i> 企业资质管理</h2>
                            </div>
                            <div>
                                <button class="btn btn-outline-secondary me-2" onclick="window.location.href='detail.html?id=1'">
                                    <i class="fas fa-arrow-left"></i> 返回详情
                                </button>
                                <button class="btn btn-primary" onclick="addQualification()">
                                    <i class="fas fa-plus"></i> 新增资质
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 企业基本信息 -->
                    <div class="company-info">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-industry fa-3x text-primary"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">上海某幕墙材料有限公司</h4>
                                <p class="text-muted mb-1">统一社会信用代码: 91310000123456789X</p>
                                <p class="text-muted mb-0">法定代表人: 张三 | 联系人: 李四 | 电话: 021-12345678</p>
                            </div>
                        </div>
                    </div>

                    <!-- 资质列表 -->
                    <div class="qualification-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4><i class="fas fa-list"></i> 企业资质列表</h4>
                            <div class="text-muted">
                                共 <span class="text-primary fw-bold">3</span> 项资质
                            </div>
                        </div>

                        <!-- 资质项目 -->
                        <div class="qualification-item">
                            <div class="qualification-header">
                                <div>
                                    <div class="qualification-title">建筑幕墙工程专业承包一级资质</div>
                                    <div class="qualification-code">资质编号: ZZ001</div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="status-badge status-valid me-2">有效</span>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewQualification(1)">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="editQualification(1)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteQualification(1)">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="qualification-details">
                                <div class="detail-item">
                                    <div class="detail-label">资质等级</div>
                                    <div class="detail-value">一级</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">发证机关</div>
                                    <div class="detail-value">住房和城乡建设部</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">发证日期</div>
                                    <div class="detail-value">2023年1月1日</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">有效期至</div>
                                    <div class="detail-value">2028年1月1日</div>
                                </div>
                            </div>
                        </div>

                        <div class="qualification-item">
                            <div class="qualification-header">
                                <div>
                                    <div class="qualification-title">安全生产许可证</div>
                                    <div class="qualification-code">证书编号: AQ002</div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="status-badge status-expiring me-2">即将到期</span>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewQualification(2)">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="editQualification(2)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                        <button class="btn btn-sm btn-outline-warning" onclick="renewQualification(2)">
                                            <i class="fas fa-redo"></i> 续期
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="qualification-details">
                                <div class="detail-item">
                                    <div class="detail-label">资质等级</div>
                                    <div class="detail-value">-</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">发证机关</div>
                                    <div class="detail-value">上海市住房和城乡建设管理委员会</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">发证日期</div>
                                    <div class="detail-value">2023年6月1日</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">有效期至</div>
                                    <div class="detail-value text-warning">2025年6月1日</div>
                                </div>
                            </div>
                        </div>

                        <div class="qualification-item">
                            <div class="qualification-header">
                                <div>
                                    <div class="qualification-title">ISO9001质量管理体系认证</div>
                                    <div class="qualification-code">证书编号: ISO003</div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="status-badge status-pending me-2">审核中</span>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewQualification(3)">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-sm btn-outline-info" onclick="checkAuditStatus(3)">
                                            <i class="fas fa-clock"></i> 审核状态
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="qualification-details">
                                <div class="detail-item">
                                    <div class="detail-label">认证范围</div>
                                    <div class="detail-value">幕墙材料生产</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">认证机构</div>
                                    <div class="detail-value">中国质量认证中心</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">申请日期</div>
                                    <div class="detail-value">2024年2月1日</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">预计完成</div>
                                    <div class="detail-value">2024年3月1日</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑资质模态框 -->
    <div class="modal fade" id="qualificationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-certificate"></i> <span id="modalTitle">新增资质</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="qualificationForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">资质名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="qualificationName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">资质编号 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="qualificationCode" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">资质等级</label>
                                <select class="form-select" id="qualificationLevel">
                                    <option value="">请选择资质等级</option>
                                    <option value="特级">特级</option>
                                    <option value="一级">一级</option>
                                    <option value="二级">二级</option>
                                    <option value="三级">三级</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">发证机关 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="issuingAuthority" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">发证日期 <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="issueDate" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">有效期至 <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="expireDate" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">资质范围</label>
                            <textarea class="form-control" id="qualificationScope" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">证书文件</label>
                            <input type="file" class="form-control" id="certificateFile" accept=".pdf,.jpg,.jpeg,.png">
                            <small class="text-muted">支持 PDF、JPG、PNG 格式，文件大小不超过 10MB</small>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">备注</label>
                            <textarea class="form-control" id="remark" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveQualification()">保存资质</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 切换侧边栏
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('show');
        }

        // 新增资质
        function addQualification() {
            document.getElementById('modalTitle').textContent = '新增资质';
            document.getElementById('qualificationForm').reset();
            const modal = new bootstrap.Modal(document.getElementById('qualificationModal'));
            modal.show();
        }

        // 编辑资质
        function editQualification(id) {
            document.getElementById('modalTitle').textContent = '编辑资质';
            // 这里可以加载资质数据
            const modal = new bootstrap.Modal(document.getElementById('qualificationModal'));
            modal.show();
        }

        // 查看资质
        function viewQualification(id) {
            alert('查看资质详情功能');
        }

        // 删除资质
        function deleteQualification(id) {
            if (confirm('确认删除该资质？删除后不可恢复。')) {
                alert('资质删除成功！');
                location.reload();
            }
        }

        // 续期资质
        function renewQualification(id) {
            if (confirm('确认为该资质申请续期？')) {
                alert('续期申请已提交！');
            }
        }

        // 检查审核状态
        function checkAuditStatus(id) {
            alert('当前审核状态：材料审核中\n预计审核时间：3-5个工作日');
        }

        // 保存资质
        function saveQualification() {
            const form = document.getElementById('qualificationForm');
            const formData = new FormData(form);
            
            // 简单验证
            const requiredFields = ['qualificationName', 'qualificationCode', 'issuingAuthority', 'issueDate', 'expireDate'];
            let isValid = true;
            
            requiredFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            if (!isValid) {
                alert('请填写所有必填字段！');
                return;
            }
            
            // 模拟保存
            alert('资质信息保存成功！');
            bootstrap.Modal.getInstance(document.getElementById('qualificationModal')).hide();
            location.reload();
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.company-info, .qualification-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
