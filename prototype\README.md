# 幕墙信息化平台原型

## 原型说明

本目录包含上海市装饰装修行业协会幕墙信息化平台的HTML原型，用于展示系统的主要功能界面和交互流程。

## 原型结构

```
prototype/
├── index.html                 # 原型导航首页
├── login.html                 # 登录页面
├── register.html              # 用户注册页面
├── dashboard.html             # 系统首页/仪表板
├── company/
│   ├── list.html             # 企业列表页面
│   ├── detail.html           # 企业详情页面
│   ├── edit.html             # 企业信息编辑页面
│   └── qualification.html    # 企业资质管理页面
├── project/
│   ├── list.html             # 项目列表页面
│   ├── detail.html           # 项目详情页面
│   ├── edit.html             # 项目信息编辑页面
│   └── progress.html         # 项目进度管理页面
├── curtain-wall/
│   ├── list.html             # 既有幕墙列表页面
│   ├── detail.html           # 既有幕墙详情页面
│   ├── inspection.html       # 检查记录页面
│   └── maintenance.html      # 维修记录页面
├── statistics/
│   ├── overview.html         # 统计概览页面
│   ├── company-stats.html    # 企业统计页面
│   ├── project-stats.html    # 项目统计页面
│   └── reports.html          # 报表生成页面
├── system/
│   ├── users.html            # 用户管理页面
│   ├── roles.html            # 角色管理页面
│   ├── settings.html         # 系统设置页面
│   └── logs.html             # 操作日志页面
├── assets/
│   ├── css/
│   │   ├── bootstrap.min.css # Bootstrap样式
│   │   ├── custom.css        # 自定义样式
│   │   └── icons.css         # 图标样式
│   ├── js/
│   │   ├── bootstrap.min.js  # Bootstrap脚本
│   │   ├── jquery.min.js     # jQuery库
│   │   ├── chart.min.js      # 图表库
│   │   └── custom.js         # 自定义脚本
│   └── images/
│       ├── logo.png          # 系统Logo
│       └── placeholder.jpg   # 占位图片
└── README.md                 # 本说明文件
```

## 技术栈

- **HTML5**: 页面结构
- **CSS3**: 样式设计
- **Bootstrap 5**: 响应式UI框架
- **JavaScript**: 交互功能
- **Font Awesome**: 图标库
- **Chart.js**: 数据可视化图表

## 已实现页面

### 认证模块
- ✅ `login.html` - 用户登录页面
- ✅ `register.html` - 用户注册页面

### 主控制台
- ✅ `dashboard.html` - 系统首页/仪表板

### 企业管理模块
- ✅ `company/list.html` - 企业列表页面
- ✅ `company/detail.html` - 企业详情页面
- ✅ `company/edit.html` - 企业编辑页面
- ✅ `company/qualification.html` - 企业资质管理页面

### 项目管理模块
- ✅ `project/list.html` - 项目列表页面
- ✅ `project/detail.html` - 项目详情页面
- ✅ `project/edit.html` - 项目编辑页面
- ✅ `project/progress.html` - 项目进度管理页面

### 既有幕墙管理模块
- ✅ `curtain-wall/list.html` - 既有幕墙列表页面
- ✅ `curtain-wall/detail.html` - 既有幕墙详情页面
- ✅ `curtain-wall/inspection.html` - 检查记录管理页面
- ✅ `curtain-wall/maintenance.html` - 维修记录管理页面

### 统计分析模块
- ✅ `statistics/overview.html` - 统计分析概览页面
- ✅ `statistics/company-stats.html` - 企业统计分析页面
- ✅ `statistics/project-stats.html` - 项目统计分析页面
- ✅ `statistics/reports.html` - 报表生成页面

### 系统管理模块
- ✅ `system/users.html` - 用户管理页面
- ✅ `system/roles.html` - 角色管理页面
- ✅ `system/settings.html` - 系统设置页面
- ✅ `system/logs.html` - 操作日志页面

**总计：23个核心页面**

## 功能特性

### 已实现功能
1. **响应式设计** - 支持桌面端和移动端
2. **现代化UI** - 使用Bootstrap 5和渐变色设计
3. **交互动画** - 页面加载动画和悬停效果
4. **数据可视化** - 使用Chart.js展示统计图表
5. **表单验证** - 客户端表单验证
6. **模态框操作** - 新增、编辑等操作使用模态框
7. **文件上传** - 支持拖拽上传和文件管理
8. **状态管理** - 项目状态、安全等级等状态展示
9. **进度跟踪** - 项目进度时间轴展示
10. **权限管理** - 用户角色和权限控制界面

### 核心模块功能

#### 企业管理
- 企业信息的增删改查
- 企业资质管理
- 企业类型分类（材料、施工、维修、检查服务）
- 文件上传和管理

#### 项目管理
- 项目全生命周期管理
- 项目进度跟踪
- 质量检查记录
- 项目文档管理
- 施工照片管理

#### 既有幕墙管理
- 既有建筑幕墙信息管理
- 安全等级评估
- 检查记录管理
- 维修记录管理

#### 统计分析
- 企业注册趋势分析
- 项目投资规模分布
- 安全等级统计
- 地区分布热力图

#### 系统管理
- 用户账号管理
- 角色权限管理
- 系统配置管理

### 设计特点
- 采用现代化的卡片式布局
- 渐变色主题设计（#667eea 到 #764ba2）
- 丰富的图标和视觉元素
- 流畅的用户交互体验
- 统一的视觉风格和交互规范

## 使用说明

1. 直接在浏览器中打开 `login.html` 开始体验
2. 默认登录信息：
   - 用户名：admin
   - 密码：123456
3. 登录后可以浏览各个功能模块
4. 所有页面都包含完整的导航和面包屑导航

## 页面导航结构

```
登录页面 (login.html)
├── 系统首页 (dashboard.html)
├── 企业管理
│   ├── 企业列表 (company/list.html)
│   ├── 企业详情 (company/detail.html)
│   ├── 企业编辑 (company/edit.html)
│   └── 资质管理 (company/qualification.html)
├── 项目管理
│   ├── 项目列表 (project/list.html)
│   ├── 项目详情 (project/detail.html)
│   ├── 项目编辑 (project/edit.html)
│   └── 进度管理 (project/progress.html)
├── 既有幕墙
│   ├── 幕墙列表 (curtain-wall/list.html)
│   ├── 幕墙详情 (curtain-wall/detail.html)
│   ├── 检查记录 (curtain-wall/inspection.html)
│   └── 维修记录 (curtain-wall/maintenance.html)
├── 统计分析
│   ├── 统计概览 (statistics/overview.html)
│   ├── 企业统计 (statistics/company-stats.html)
│   └── 项目统计 (statistics/project-stats.html)
└── 系统管理
    ├── 用户管理 (system/users.html)
    ├── 角色管理 (system/roles.html)
    └── 系统设置 (system/settings.html)
```

## 注意事项

- 这是一个静态原型，数据为模拟数据
- 所有的表单提交和数据操作都是模拟的
- 建议使用现代浏览器（Chrome、Firefox、Safari、Edge）访问
- 页面间的跳转链接已经配置完整
- 所有交互功能都有相应的JavaScript处理

## 后续开发

此原型为前端展示版本，后续需要：
1. 集成后端API接口
2. 实现真实的数据库操作
3. 添加用户权限控制
4. 完善错误处理机制
5. 优化性能和安全性
6. 添加更多业务功能模块
7. 实现实时数据更新
8. 添加消息通知系统
