<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业详情 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar {
            background: linear-gradient(45deg, #667eea, #764ba2);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 0;
        }
        .sidebar-menu {
            list-style: none;
            padding: 1rem 0;
            margin: 0;
        }
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        .sidebar-menu a {
            display: block;
            padding: 0.8rem 1.5rem;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background: rgba(102, 126, 234, 0.1);
            border-left-color: #667eea;
            color: #667eea;
        }
        .sidebar-menu i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            padding: 2rem;
        }
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .info-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .company-header {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid #e9ecef;
        }
        .company-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin-right: 1.5rem;
        }
        .company-info h2 {
            margin-bottom: 0.5rem;
            color: #333;
        }
        .company-type-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-right: 1rem;
        }
        .type-material { background: rgba(102, 126, 234, 0.1); color: #667eea; }
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .status-active { background: rgba(40, 167, 69, 0.1); color: #28a745; }
        .info-row {
            display: flex;
            margin-bottom: 1rem;
            padding: 0.8rem 0;
            border-bottom: 1px solid #f8f9fa;
        }
        .info-label {
            width: 150px;
            font-weight: 600;
            color: #666;
            flex-shrink: 0;
        }
        .info-value {
            flex: 1;
            color: #333;
        }
        .nav-tabs .nav-link {
            border: none;
            color: #666;
            font-weight: 500;
            padding: 1rem 1.5rem;
        }
        .nav-tabs .nav-link.active {
            background: none;
            border-bottom: 3px solid #667eea;
            color: #667eea;
        }
        .tab-content {
            padding: 2rem 0;
        }
        .qualification-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }
        .personnel-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }
        .personnel-avatar {
            width: 50px;
            height: 50px;
            background: #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 1rem;
        }
        .performance-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #28a745;
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 8px;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -250px;
                width: 250px;
                z-index: 1000;
                transition: left 0.3s ease;
            }
            .sidebar.show {
                left: 0;
            }
            .main-content {
                padding: 1rem;
            }
            .company-header {
                flex-direction: column;
                text-align: center;
            }
            .company-logo {
                margin-right: 0;
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <button class="navbar-toggler d-lg-none" type="button" onclick="toggleSidebar()">
                <span class="navbar-toggler-icon"></span>
            </button>
            <a class="navbar-brand" href="../dashboard.html">
                <i class="fas fa-building"></i> 幕墙信息化平台
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user"></i> 个人资料</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-cog"></i> 系统设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar" id="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="list.html" class="active"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="../project/list.html"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="../curtain-wall/list.html"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="../statistics/overview.html"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="../system/users.html"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb mb-2">
                                        <li class="breadcrumb-item"><a href="list.html">企业管理</a></li>
                                        <li class="breadcrumb-item active">企业详情</li>
                                    </ol>
                                </nav>
                                <h2><i class="fas fa-info-circle"></i> 企业详情</h2>
                            </div>
                            <div>
                                <button class="btn btn-outline-primary me-2" onclick="window.location.href='edit.html?id=1'">
                                    <i class="fas fa-edit"></i> 编辑企业
                                </button>
                                <button class="btn btn-primary" onclick="window.location.href='qualification.html?id=1'">
                                    <i class="fas fa-certificate"></i> 管理资质
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 企业基本信息 -->
                    <div class="info-card">
                        <div class="company-header">
                            <div class="company-logo">
                                <i class="fas fa-industry"></i>
                            </div>
                            <div class="company-info">
                                <h2>上海某幕墙材料有限公司</h2>
                                <div class="mb-2">
                                    <span class="company-type-badge type-material">幕墙材料企业</span>
                                    <span class="status-badge status-active">正常</span>
                                </div>
                                <p class="text-muted mb-0">专业从事幕墙材料生产和销售的现代化企业</p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-row">
                                    <div class="info-label">统一社会信用代码:</div>
                                    <div class="info-value">91310000123456789X</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">法定代表人:</div>
                                    <div class="info-value">张三</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">注册资本:</div>
                                    <div class="info-value">5000万元</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">成立日期:</div>
                                    <div class="info-value">2010年3月15日</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-row">
                                    <div class="info-label">联系人:</div>
                                    <div class="info-value">李四</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">联系电话:</div>
                                    <div class="info-value">021-12345678</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">邮箱地址:</div>
                                    <div class="info-value"><EMAIL></div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">注册时间:</div>
                                    <div class="info-value">2024年1月15日</div>
                                </div>
                            </div>
                        </div>

                        <div class="info-row">
                            <div class="info-label">企业地址:</div>
                            <div class="info-value">上海市浦东新区某某路123号某某大厦15楼</div>
                        </div>
                    </div>

                    <!-- 详细信息标签页 -->
                    <div class="info-card">
                        <ul class="nav nav-tabs" id="companyTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="qualification-tab" data-bs-toggle="tab" data-bs-target="#qualification" type="button" role="tab">
                                    <i class="fas fa-certificate"></i> 企业资质
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="personnel-tab" data-bs-toggle="tab" data-bs-target="#personnel" type="button" role="tab">
                                    <i class="fas fa-users"></i> 企业人员
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="performance-tab" data-bs-toggle="tab" data-bs-target="#performance" type="button" role="tab">
                                    <i class="fas fa-trophy"></i> 企业业绩
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="documents-tab" data-bs-toggle="tab" data-bs-target="#documents" type="button" role="tab">
                                    <i class="fas fa-file-alt"></i> 相关文档
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content" id="companyTabsContent">
                            <!-- 企业资质 -->
                            <div class="tab-pane fade show active" id="qualification" role="tabpanel">
                                <div class="qualification-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5>建筑幕墙工程专业承包一级资质</h5>
                                            <p class="text-muted mb-2">资质编号: ZZ001</p>
                                            <p class="mb-1"><strong>发证机关:</strong> 住房和城乡建设部</p>
                                            <p class="mb-1"><strong>发证日期:</strong> 2023年1月1日</p>
                                            <p class="mb-0"><strong>有效期至:</strong> 2028年1月1日</p>
                                        </div>
                                        <span class="badge bg-success">有效</span>
                                    </div>
                                </div>
                                <div class="qualification-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5>安全生产许可证</h5>
                                            <p class="text-muted mb-2">证书编号: AQ002</p>
                                            <p class="mb-1"><strong>发证机关:</strong> 上海市住房和城乡建设管理委员会</p>
                                            <p class="mb-1"><strong>发证日期:</strong> 2023年6月1日</p>
                                            <p class="mb-0"><strong>有效期至:</strong> 2026年6月1日</p>
                                        </div>
                                        <span class="badge bg-warning">即将到期</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 企业人员 -->
                            <div class="tab-pane fade" id="personnel" role="tabpanel">
                                <div class="personnel-item">
                                    <div class="personnel-avatar">张</div>
                                    <div>
                                        <h5 class="mb-1">张三</h5>
                                        <p class="mb-1"><strong>职位:</strong> 总经理</p>
                                        <p class="mb-1"><strong>联系电话:</strong> 138****1234</p>
                                        <p class="mb-0"><strong>专业资质:</strong> 一级建造师（建筑工程）</p>
                                    </div>
                                </div>
                                <div class="personnel-item">
                                    <div class="personnel-avatar">李</div>
                                    <div>
                                        <h5 class="mb-1">李四</h5>
                                        <p class="mb-1"><strong>职位:</strong> 技术总监</p>
                                        <p class="mb-1"><strong>联系电话:</strong> 139****5678</p>
                                        <p class="mb-0"><strong>专业资质:</strong> 高级工程师、注册结构工程师</p>
                                    </div>
                                </div>
                                <div class="personnel-item">
                                    <div class="personnel-avatar">王</div>
                                    <div>
                                        <h5 class="mb-1">王五</h5>
                                        <p class="mb-1"><strong>职位:</strong> 项目经理</p>
                                        <p class="mb-1"><strong>联系电话:</strong> 137****9012</p>
                                        <p class="mb-0"><strong>专业资质:</strong> 二级建造师（建筑工程）</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 企业业绩 -->
                            <div class="tab-pane fade" id="performance" role="tabpanel">
                                <div class="performance-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5>上海某商业大厦幕墙工程</h5>
                                            <p class="mb-1"><strong>项目地点:</strong> 上海市黄浦区</p>
                                            <p class="mb-1"><strong>项目金额:</strong> 2500万元</p>
                                            <p class="mb-1"><strong>完成时间:</strong> 2023年12月</p>
                                            <p class="mb-0"><strong>项目描述:</strong> 高层商业建筑玻璃幕墙工程，总面积15000平方米</p>
                                        </div>
                                        <span class="badge bg-success">已完成</span>
                                    </div>
                                </div>
                                <div class="performance-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5>上海某写字楼幕墙改造工程</h5>
                                            <p class="mb-1"><strong>项目地点:</strong> 上海市浦东新区</p>
                                            <p class="mb-1"><strong>项目金额:</strong> 1800万元</p>
                                            <p class="mb-1"><strong>完成时间:</strong> 2023年8月</p>
                                            <p class="mb-0"><strong>项目描述:</strong> 既有建筑幕墙更新改造，提升建筑外观和性能</p>
                                        </div>
                                        <span class="badge bg-success">已完成</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 相关文档 -->
                            <div class="tab-pane fade" id="documents" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-title">
                                                    <i class="fas fa-file-pdf text-danger"></i> 营业执照
                                                </h6>
                                                <p class="card-text text-muted">上传时间: 2024-01-15</p>
                                                <button class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-download"></i> 下载
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-title">
                                                    <i class="fas fa-file-pdf text-danger"></i> 资质证书
                                                </h6>
                                                <p class="card-text text-muted">上传时间: 2024-01-15</p>
                                                <button class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-download"></i> 下载
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-title">
                                                    <i class="fas fa-file-pdf text-danger"></i> 安全生产许可证
                                                </h6>
                                                <p class="card-text text-muted">上传时间: 2024-01-15</p>
                                                <button class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-download"></i> 下载
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-title">
                                                    <i class="fas fa-file-image text-primary"></i> 企业照片
                                                </h6>
                                                <p class="card-text text-muted">上传时间: 2024-01-15</p>
                                                <button class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 切换侧边栏
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('show');
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.info-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
