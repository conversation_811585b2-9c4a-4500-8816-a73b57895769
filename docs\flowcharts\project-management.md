# 项目信息录入和查询流程图

## 项目信息录入流程

```mermaid
flowchart TD
    A[用户登录系统] --> B[进入项目管理]
    B --> C[点击新增项目]
    C --> D[填写项目基本信息]
    
    D --> E[项目名称]
    E --> F[项目编号]
    F --> G[项目地点]
    G --> H[建设单位]
    H --> I[设计单位]
    I --> J[施工单位]
    J --> K[监理单位]
    K --> L[项目类型]
    L --> M[建筑面积]
    M --> N[幕墙面积]
    N --> O[幕墙类型]
    O --> P[开工日期]
    P --> Q[计划竣工日期]
    Q --> R[项目投资额]
    R --> S[项目描述]
    
    S --> T[上传项目图片]
    T --> U[上传设计图纸]
    U --> V[上传相关文档]
    V --> W[预览项目信息]
    W --> X{信息确认无误?}
    X -->|否| Y[返回修改]
    Y --> D
    X -->|是| Z[提交项目信息]
    
    Z --> AA[系统验证]
    AA --> BB{验证结果}
    BB -->|失败| CC[显示错误信息]
    CC --> D
    BB -->|成功| DD[保存项目信息]
    DD --> EE[生成项目ID]
    EE --> FF[发送创建通知]
    FF --> GG[项目创建成功]
    
    GG --> HH[进入项目详情页]
    HH --> II[设置项目进度节点]
    II --> JJ[添加项目成员]
    JJ --> KK[配置项目权限]
    KK --> LL[项目初始化完成]
    
    style A fill:#e1f5fe
    style LL fill:#e8f5e8
    style CC fill:#ffebee
```

## 项目查询流程

```mermaid
flowchart TD
    A[进入项目查询页面] --> B[选择查询方式]
    B --> C{查询方式}
    C -->|快速查询| D[输入关键词]
    C -->|高级查询| E[设置查询条件]
    C -->|我的项目| F[查看个人项目]
    
    D --> G[项目名称/编号]
    G --> H[点击搜索]
    
    E --> I[项目类型筛选]
    I --> J[地区筛选]
    J --> K[时间范围筛选]
    K --> L[投资额范围筛选]
    L --> M[项目状态筛选]
    M --> N[参与单位筛选]
    N --> O[幕墙类型筛选]
    O --> P[点击查询]
    
    F --> Q[加载用户相关项目]
    Q --> R{权限检查}
    R -->|无权限| S[显示权限不足]
    R -->|有权限| T[显示项目列表]
    
    H --> U[执行搜索]
    P --> U
    U --> V[数据库查询]
    V --> W{查询结果}
    W -->|无结果| X[显示无匹配项目]
    W -->|有结果| Y[显示项目列表]
    
    T --> Z[项目列表展示]
    Y --> Z
    Z --> AA[项目基本信息]
    AA --> BB[项目状态]
    BB --> CC[参与单位]
    CC --> DD[操作按钮]
    
    DD --> EE{用户操作}
    EE -->|查看详情| FF[进入项目详情]
    EE -->|编辑项目| GG[进入编辑页面]
    EE -->|删除项目| HH[确认删除操作]
    EE -->|导出数据| II[导出项目信息]
    
    FF --> JJ[显示项目详细信息]
    JJ --> KK[显示项目进度]
    KK --> LL[显示质量检查记录]
    LL --> MM[显示项目文档]
    MM --> NN[显示项目成员]
    
    GG --> OO{权限检查}
    OO -->|无权限| PP[显示权限不足]
    OO -->|有权限| QQ[加载编辑表单]
    QQ --> RR[修改项目信息]
    RR --> SS[保存修改]
    SS --> TT[更新成功]
    
    HH --> UU{确认删除?}
    UU -->|否| Z
    UU -->|是| VV{权限检查}
    VV -->|无权限| WW[显示权限不足]
    VV -->|有权限| XX[软删除项目]
    XX --> YY[记录删除日志]
    YY --> ZZ[删除成功]
    
    II --> AAA[生成导出文件]
    AAA --> BBB[下载文件]
    
    X --> CCC[提供搜索建议]
    S --> DDD[申请权限链接]
    
    style A fill:#e1f5fe
    style TT fill:#e8f5e8
    style ZZ fill:#fff3e0
    style BBB fill:#e8f5e8
    style PP fill:#ffebee
    style WW fill:#ffebee
```

## 项目进度管理流程

```mermaid
flowchart TD
    A[进入项目详情] --> B[点击进度管理]
    B --> C[查看进度列表]
    C --> D[选择操作]
    D --> E{操作类型}
    E -->|新增进度| F[点击新增进度]
    E -->|编辑进度| G[选择进度编辑]
    E -->|删除进度| H[选择进度删除]
    E -->|查看详情| I[查看进度详情]
    
    F --> J[选择进度节点类型]
    J --> K[填写进度信息]
    K --> L[进度名称]
    L --> M[计划开始日期]
    M --> N[计划完成日期]
    N --> O[负责人]
    O --> P[进度描述]
    P --> Q[上传相关文档]
    Q --> R[提交进度信息]
    
    G --> S[加载进度信息]
    S --> T[修改进度信息]
    T --> U[更新实际日期]
    U --> V[更新完成率]
    V --> W[添加进度备注]
    W --> X[上传进度图片]
    X --> Y[保存修改]
    
    H --> Z{确认删除?}
    Z -->|否| C
    Z -->|是| AA[删除进度记录]
    AA --> BB[记录删除日志]
    
    I --> CC[显示进度详细信息]
    CC --> DD[显示进度时间轴]
    DD --> EE[显示相关文档]
    EE --> FF[显示进度图片]
    
    R --> GG[系统验证]
    GG --> HH{验证结果}
    HH -->|失败| II[显示错误信息]
    II --> K
    HH -->|成功| JJ[保存进度信息]
    JJ --> KK[更新项目状态]
    KK --> LL[发送进度通知]
    LL --> MM[进度添加成功]
    
    Y --> NN[记录修改历史]
    NN --> OO[更新项目进度]
    OO --> PP[发送更新通知]
    PP --> QQ[进度更新成功]
    
    BB --> RR[进度删除完成]
    
    style A fill:#e1f5fe
    style MM fill:#e8f5e8
    style QQ fill:#e8f5e8
    style RR fill:#fff3e0
```

## 项目质量检查流程

```mermaid
flowchart TD
    A[进入项目质量管理] --> B[查看检查记录列表]
    B --> C[选择操作]
    C --> D{操作类型}
    D -->|新增检查| E[点击新增检查]
    D -->|编辑检查| F[选择检查编辑]
    D -->|查看详情| G[查看检查详情]
    D -->|生成报告| H[生成质量报告]
    
    E --> I[选择检查类型]
    I --> J[填写检查信息]
    J --> K[检查项目]
    K --> L[检查标准]
    L --> M[检查人员]
    M --> N[检查日期]
    N --> O[检查方法]
    O --> P[检查结果]
    P --> Q{检查结果}
    Q -->|合格| R[记录合格信息]
    Q -->|不合格| S[填写问题描述]
    
    R --> T[上传检查照片]
    S --> U[制定整改措施]
    U --> V[设置整改期限]
    V --> W[指定整改责任人]
    W --> T
    
    T --> X[上传检查报告]
    X --> Y[提交检查记录]
    
    F --> Z[加载检查信息]
    Z --> AA[修改检查信息]
    AA --> BB{问题是否整改?}
    BB -->|是| CC[更新整改状态]
    BB -->|否| DD[更新整改进度]
    CC --> EE[上传整改证明]
    DD --> FF[记录整改情况]
    EE --> GG[保存修改]
    FF --> GG
    
    G --> HH[显示检查详细信息]
    HH --> II[显示检查标准]
    II --> JJ[显示检查结果]
    JJ --> KK[显示问题描述]
    KK --> LL[显示整改措施]
    LL --> MM[显示整改进度]
    MM --> NN[显示相关附件]
    
    H --> OO[选择报告类型]
    OO --> PP[设置报告参数]
    PP --> QQ[生成质量报告]
    QQ --> RR[预览报告内容]
    RR --> SS{报告确认?}
    SS -->|否| PP
    SS -->|是| TT[导出报告文件]
    
    Y --> UU[系统验证]
    UU --> VV{验证结果}
    VV -->|失败| WW[显示错误信息]
    WW --> J
    VV -->|成功| XX[保存检查记录]
    XX --> YY[更新项目质量状态]
    YY --> ZZ[发送检查通知]
    ZZ --> AAA[检查记录创建成功]
    
    GG --> BBB[记录修改历史]
    BBB --> CCC[更新质量状态]
    CCC --> DDD[发送更新通知]
    DDD --> EEE[检查记录更新成功]
    
    TT --> FFF[报告生成完成]
    
    style A fill:#e1f5fe
    style AAA fill:#e8f5e8
    style EEE fill:#e8f5e8
    style FFF fill:#e8f5e8
    style WW fill:#ffebee
```

## 流程说明

### 1. 项目信息录入流程特点
- **完整信息收集**: 收集项目全生命周期基本信息
- **多媒体支持**: 支持图片、文档等多种文件上传
- **信息验证**: 系统自动验证信息完整性和合规性
- **权限控制**: 根据用户类型控制录入权限

### 2. 项目查询流程特点
- **多种查询方式**: 支持快速查询、高级查询、个人项目查询
- **灵活筛选条件**: 提供多维度筛选条件
- **权限控制**: 根据用户权限显示可访问的项目
- **操作便捷**: 提供查看、编辑、删除、导出等操作

### 3. 项目进度管理流程特点
- **节点化管理**: 按进度节点管理项目进展
- **实时更新**: 支持实时更新进度状态和完成率
- **文档管理**: 每个进度节点可上传相关文档
- **通知机制**: 进度变更自动通知相关人员

### 4. 项目质量检查流程特点
- **标准化检查**: 基于行业标准进行质量检查
- **问题跟踪**: 对发现的问题进行跟踪整改
- **证据保存**: 保存检查照片和相关证明文件
- **报告生成**: 自动生成质量检查报告

### 5. 数据安全和审计
- **操作日志**: 记录所有操作日志
- **权限控制**: 严格的权限控制机制
- **数据备份**: 重要数据自动备份
- **审计追踪**: 完整的数据变更审计链
