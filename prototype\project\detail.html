<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目详情 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar {
            background: linear-gradient(45deg, #667eea, #764ba2);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 0;
        }
        .sidebar-menu {
            list-style: none;
            padding: 1rem 0;
            margin: 0;
        }
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        .sidebar-menu a {
            display: block;
            padding: 0.8rem 1.5rem;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background: rgba(102, 126, 234, 0.1);
            border-left-color: #667eea;
            color: #667eea;
        }
        .sidebar-menu i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            padding: 2rem;
        }
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .info-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .project-header {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid #e9ecef;
        }
        .project-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin-right: 1.5rem;
        }
        .project-info h2 {
            margin-bottom: 0.5rem;
            color: #333;
        }
        .project-status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-right: 1rem;
        }
        .status-construction { background: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .info-row {
            display: flex;
            margin-bottom: 1rem;
            padding: 0.8rem 0;
            border-bottom: 1px solid #f8f9fa;
        }
        .info-label {
            width: 150px;
            font-weight: 600;
            color: #666;
            flex-shrink: 0;
        }
        .info-value {
            flex: 1;
            color: #333;
        }
        .nav-tabs .nav-link {
            border: none;
            color: #666;
            font-weight: 500;
            padding: 1rem 1.5rem;
        }
        .nav-tabs .nav-link.active {
            background: none;
            border-bottom: 3px solid #667eea;
            color: #667eea;
        }
        .tab-content {
            padding: 2rem 0;
        }
        .progress-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }
        .quality-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #28a745;
        }
        .document-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 8px;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .progress-bar-container {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 1rem 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 10px;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -250px;
                width: 250px;
                z-index: 1000;
                transition: left 0.3s ease;
            }
            .sidebar.show {
                left: 0;
            }
            .main-content {
                padding: 1rem;
            }
            .project-header {
                flex-direction: column;
                text-align: center;
            }
            .project-icon {
                margin-right: 0;
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <button class="navbar-toggler d-lg-none" type="button" onclick="toggleSidebar()">
                <span class="navbar-toggler-icon"></span>
            </button>
            <a class="navbar-brand" href="../dashboard.html">
                <i class="fas fa-building"></i> 幕墙信息化平台
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user"></i> 个人资料</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-cog"></i> 系统设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar" id="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="../company/list.html"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="list.html" class="active"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="../curtain-wall/list.html"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="../statistics/overview.html"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="../system/users.html"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb mb-2">
                                        <li class="breadcrumb-item"><a href="list.html">项目管理</a></li>
                                        <li class="breadcrumb-item active">项目详情</li>
                                    </ol>
                                </nav>
                                <h2><i class="fas fa-info-circle"></i> 项目详情</h2>
                            </div>
                            <div>
                                <button class="btn btn-outline-primary me-2" onclick="window.location.href='edit.html?id=1'">
                                    <i class="fas fa-edit"></i> 编辑项目
                                </button>
                                <button class="btn btn-primary" onclick="window.location.href='progress.html?id=1'">
                                    <i class="fas fa-tasks"></i> 管理进度
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 项目基本信息 -->
                    <div class="info-card">
                        <div class="project-header">
                            <div class="project-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="project-info">
                                <h2>上海某商业大厦幕墙工程</h2>
                                <div class="mb-2">
                                    <span class="project-status-badge status-construction">施工中</span>
                                    <span class="badge bg-info">玻璃幕墙</span>
                                </div>
                                <p class="text-muted mb-0">高层商业建筑玻璃幕墙工程项目</p>
                            </div>
                        </div>

                        <!-- 项目进度 -->
                        <div class="mb-4">
                            <h5><i class="fas fa-chart-line"></i> 项目进度</h5>
                            <div class="progress-bar-container">
                                <div class="progress-bar" style="width: 65%">65%</div>
                            </div>
                            <div class="row text-center">
                                <div class="col-3">
                                    <small class="text-muted">开工日期</small>
                                    <div class="fw-bold">2024-01-15</div>
                                </div>
                                <div class="col-3">
                                    <small class="text-muted">计划完工</small>
                                    <div class="fw-bold">2024-12-31</div>
                                </div>
                                <div class="col-3">
                                    <small class="text-muted">已用时间</small>
                                    <div class="fw-bold">45天</div>
                                </div>
                                <div class="col-3">
                                    <small class="text-muted">剩余时间</small>
                                    <div class="fw-bold">320天</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-row">
                                    <div class="info-label">项目编号:</div>
                                    <div class="info-value">PJ001</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">项目地点:</div>
                                    <div class="info-value">上海市黄浦区某某路123号</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">业主单位:</div>
                                    <div class="info-value">上海某房地产开发有限公司</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">设计单位:</div>
                                    <div class="info-value">上海某建筑设计院</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">施工单位:</div>
                                    <div class="info-value">上海某建筑幕墙工程有限公司</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-row">
                                    <div class="info-label">监理单位:</div>
                                    <div class="info-value">上海某监理公司</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">项目面积:</div>
                                    <div class="info-value">50,000㎡</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">幕墙面积:</div>
                                    <div class="info-value">15,000㎡</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">幕墙类型:</div>
                                    <div class="info-value">玻璃幕墙</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">创建时间:</div>
                                    <div class="info-value">2024年1月10日</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 详细信息标签页 -->
                    <div class="info-card">
                        <ul class="nav nav-tabs" id="projectTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="progress-tab" data-bs-toggle="tab" data-bs-target="#progress" type="button" role="tab">
                                    <i class="fas fa-tasks"></i> 项目进度
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="quality-tab" data-bs-toggle="tab" data-bs-target="#quality" type="button" role="tab">
                                    <i class="fas fa-check-circle"></i> 质量检查
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="documents-tab" data-bs-toggle="tab" data-bs-target="#documents" type="button" role="tab">
                                    <i class="fas fa-file-alt"></i> 项目文档
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="photos-tab" data-bs-toggle="tab" data-bs-target="#photos" type="button" role="tab">
                                    <i class="fas fa-camera"></i> 施工照片
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content" id="projectTabsContent">
                            <!-- 项目进度 -->
                            <div class="tab-pane fade show active" id="progress" role="tabpanel">
                                <div class="progress-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5>基础施工</h5>
                                            <p class="mb-1"><strong>计划日期:</strong> 2024年1月15日 - 2024年3月1日</p>
                                            <p class="mb-1"><strong>实际日期:</strong> 2024年1月15日 - 2024年3月5日</p>
                                            <p class="mb-0"><strong>完成率:</strong> 100%</p>
                                        </div>
                                        <span class="badge bg-success">已完成</span>
                                    </div>
                                </div>
                                <div class="progress-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5>主体结构施工</h5>
                                            <p class="mb-1"><strong>计划日期:</strong> 2024年3月1日 - 2024年8月1日</p>
                                            <p class="mb-1"><strong>实际日期:</strong> 2024年3月5日 - 进行中</p>
                                            <p class="mb-0"><strong>完成率:</strong> 75%</p>
                                        </div>
                                        <span class="badge bg-warning">进行中</span>
                                    </div>
                                </div>
                                <div class="progress-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5>幕墙安装</h5>
                                            <p class="mb-1"><strong>计划日期:</strong> 2024年8月1日 - 2024年11月1日</p>
                                            <p class="mb-1"><strong>实际日期:</strong> 待开始</p>
                                            <p class="mb-0"><strong>完成率:</strong> 0%</p>
                                        </div>
                                        <span class="badge bg-secondary">未开始</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 质量检查 -->
                            <div class="tab-pane fade" id="quality" role="tabpanel">
                                <div class="quality-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5>基础工程质量检查</h5>
                                            <p class="mb-1"><strong>检查日期:</strong> 2024年3月3日</p>
                                            <p class="mb-1"><strong>检查人员:</strong> 张工程师</p>
                                            <p class="mb-1"><strong>检查标准:</strong> GB50210-2018</p>
                                            <p class="mb-0"><strong>检查结果:</strong> 合格</p>
                                        </div>
                                        <span class="badge bg-success">合格</span>
                                    </div>
                                </div>
                                <div class="quality-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5>主体结构质量检查</h5>
                                            <p class="mb-1"><strong>检查日期:</strong> 2024年2月25日</p>
                                            <p class="mb-1"><strong>检查人员:</strong> 李工程师</p>
                                            <p class="mb-1"><strong>检查标准:</strong> GB50204-2015</p>
                                            <p class="mb-0"><strong>问题描述:</strong> 部分钢筋绑扎不规范，需要整改</p>
                                        </div>
                                        <span class="badge bg-warning">整改中</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 项目文档 -->
                            <div class="tab-pane fade" id="documents" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="document-item">
                                            <div>
                                                <h6><i class="fas fa-file-pdf text-danger"></i> 施工图纸</h6>
                                                <small class="text-muted">上传时间: 2024-01-10</small>
                                            </div>
                                            <button class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-download"></i> 下载
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="document-item">
                                            <div>
                                                <h6><i class="fas fa-file-pdf text-danger"></i> 施工方案</h6>
                                                <small class="text-muted">上传时间: 2024-01-12</small>
                                            </div>
                                            <button class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-download"></i> 下载
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="document-item">
                                            <div>
                                                <h6><i class="fas fa-file-excel text-success"></i> 材料清单</h6>
                                                <small class="text-muted">上传时间: 2024-01-15</small>
                                            </div>
                                            <button class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-download"></i> 下载
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="document-item">
                                            <div>
                                                <h6><i class="fas fa-file-word text-primary"></i> 质量检查报告</h6>
                                                <small class="text-muted">上传时间: 2024-03-05</small>
                                            </div>
                                            <button class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-download"></i> 下载
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 施工照片 -->
                            <div class="tab-pane fade" id="photos" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <div class="card">
                                            <img src="https://via.placeholder.com/300x200?text=基础施工" class="card-img-top" alt="基础施工">
                                            <div class="card-body p-2">
                                                <small class="text-muted">基础施工 - 2024-02-01</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="card">
                                            <img src="https://via.placeholder.com/300x200?text=主体结构" class="card-img-top" alt="主体结构">
                                            <div class="card-body p-2">
                                                <small class="text-muted">主体结构 - 2024-02-15</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="card">
                                            <img src="https://via.placeholder.com/300x200?text=钢结构安装" class="card-img-top" alt="钢结构安装">
                                            <div class="card-body p-2">
                                                <small class="text-muted">钢结构安装 - 2024-03-01</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="card">
                                            <img src="https://via.placeholder.com/300x200?text=现场全景" class="card-img-top" alt="现场全景">
                                            <div class="card-body p-2">
                                                <small class="text-muted">现场全景 - 2024-03-10</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 切换侧边栏
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('show');
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.info-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
