<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>维修记录 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Microsoft YaHei', sans-serif; }
        .navbar { background: linear-gradient(45deg, #667eea, #764ba2); box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .sidebar { background: white; min-height: calc(100vh - 76px); box-shadow: 2px 0 10px rgba(0,0,0,0.1); padding: 0; }
        .sidebar-menu { list-style: none; padding: 1rem 0; margin: 0; }
        .sidebar-menu li { margin-bottom: 0.5rem; }
        .sidebar-menu a { display: block; padding: 0.8rem 1.5rem; color: #333; text-decoration: none; transition: all 0.3s ease; border-left: 3px solid transparent; }
        .sidebar-menu a:hover, .sidebar-menu a.active { background: rgba(102, 126, 234, 0.1); border-left-color: #667eea; color: #667eea; }
        .sidebar-menu i { width: 20px; margin-right: 10px; }
        .main-content { padding: 2rem; }
        .page-header { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .building-info { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .search-filters { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .maintenance-table { background: white; border-radius: 15px; padding: 1.5rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .btn-primary { background: linear-gradient(45deg, #667eea, #764ba2); border: none; border-radius: 8px; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3); }
        .maintenance-status-badge { padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.8rem; font-weight: 500; }
        .status-completed { background: rgba(40, 167, 69, 0.1); color: #28a745; }
        .status-progress { background: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .status-planned { background: rgba(108, 117, 125, 0.1); color: #6c757d; }
        .status-urgent { background: rgba(220, 53, 69, 0.1); color: #dc3545; }
        .table th { border-top: none; font-weight: 600; color: #333; background: #f8f9fa; }
        .action-buttons .btn { margin-right: 0.5rem; margin-bottom: 0.5rem; }
        .maintenance-detail { background: #f8f9fa; border-radius: 10px; padding: 1rem; margin-top: 0.5rem; }
        .safety-level-badge { padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.8rem; font-weight: 500; }
        .level-safe { background: rgba(40, 167, 69, 0.1); color: #28a745; }
        .cost-highlight { font-weight: 600; color: #667eea; }
        .warranty-info { background: #e7f3ff; border-radius: 5px; padding: 0.5rem; font-size: 0.9rem; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.html"><i class="fas fa-building"></i> 幕墙信息化平台</a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="../company/list.html"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="../project/list.html"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="list.html" class="active"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="../statistics/overview.html"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="../system/users.html"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb mb-2">
                                        <li class="breadcrumb-item"><a href="list.html">既有幕墙</a></li>
                                        <li class="breadcrumb-item"><a href="detail.html?id=1">幕墙详情</a></li>
                                        <li class="breadcrumb-item active">维修记录</li>
                                    </ol>
                                </nav>
                                <h2><i class="fas fa-tools"></i> 维修记录管理</h2>
                            </div>
                            <div>
                                <button class="btn btn-outline-secondary me-2" onclick="window.location.href='detail.html?id=1'">
                                    <i class="fas fa-arrow-left"></i> 返回详情
                                </button>
                                <button class="btn btn-primary" onclick="addMaintenance()">
                                    <i class="fas fa-plus"></i> 新增维修
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 建筑基本信息 -->
                    <div class="building-info">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-building fa-3x text-primary"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">上海某写字楼</h4>
                                <p class="text-muted mb-1">建筑地址: 上海市浦东新区某某路456号</p>
                                <p class="text-muted mb-0">
                                    <span class="safety-level-badge level-safe me-2">安全</span>
                                    幕墙类型: 玻璃幕墙 | 建成年份: 2015年 | 幕墙面积: 8,500㎡
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- 搜索筛选 -->
                    <div class="search-filters">
                        <form class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">维修类型</label>
                                <select class="form-select">
                                    <option value="">全部类型</option>
                                    <option value="1">预防性维修</option>
                                    <option value="2">纠正性维修</option>
                                    <option value="3">应急维修</option>
                                    <option value="4">改善性维修</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">维修状态</label>
                                <select class="form-select">
                                    <option value="">全部状态</option>
                                    <option value="1">已完成</option>
                                    <option value="2">进行中</option>
                                    <option value="3">计划中</option>
                                    <option value="4">紧急</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">维修时间</label>
                                <select class="form-select">
                                    <option value="">全部时间</option>
                                    <option value="2024">2024年</option>
                                    <option value="2023">2023年</option>
                                    <option value="2022">2022年</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    <button type="reset" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo"></i> 重置
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 维修记录列表 -->
                    <div class="maintenance-table">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5><i class="fas fa-list"></i> 维修记录列表</h5>
                            <div class="text-muted">
                                共 <span class="text-primary fw-bold">6</span> 条记录 | 总费用: <span class="cost-highlight">¥45,800</span>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>维修编号</th>
                                        <th>维修类型</th>
                                        <th>维修日期</th>
                                        <th>维修单位</th>
                                        <th>维修费用</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="fw-bold">MNT-2024-001</div>
                                            <small class="text-muted">密封胶更换</small>
                                        </td>
                                        <td>预防性维修</td>
                                        <td>2024-01-20</td>
                                        <td>上海某幕墙维修公司</td>
                                        <td><span class="cost-highlight">¥15,000</span></td>
                                        <td><span class="maintenance-status-badge status-completed">已完成</span></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewMaintenance(1)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="editMaintenance(1)">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </button>
                                                <button class="btn btn-sm btn-outline-info" onclick="viewWarranty(1)">
                                                    <i class="fas fa-shield-alt"></i> 质保
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="7">
                                            <div class="maintenance-detail">
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <strong>维修内容:</strong> 更换老化密封胶约200米，包括清理旧胶、重新打胶、质量检验<br>
                                                        <strong>使用材料:</strong> 进口结构胶、耐候胶<br>
                                                        <strong>施工人员:</strong> 3人，工期5天
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="warranty-info">
                                                            <i class="fas fa-shield-alt text-success"></i> 质保期: 2年<br>
                                                            <small>质保到期: 2026-01-20</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="fw-bold">MNT-2023-008</div>
                                            <small class="text-muted">玻璃更换</small>
                                        </td>
                                        <td>纠正性维修</td>
                                        <td>2023-08-15</td>
                                        <td>上海某幕墙维修公司</td>
                                        <td><span class="cost-highlight">¥8,500</span></td>
                                        <td><span class="maintenance-status-badge status-completed">已完成</span></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewMaintenance(2)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-info" onclick="viewWarranty(2)">
                                                    <i class="fas fa-shield-alt"></i> 质保
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="fw-bold">MNT-2024-002</div>
                                            <small class="text-muted">支撑结构检修</small>
                                        </td>
                                        <td>预防性维修</td>
                                        <td>2024-03-10</td>
                                        <td>上海某建筑维修公司</td>
                                        <td><span class="cost-highlight">¥12,300</span></td>
                                        <td><span class="maintenance-status-badge status-progress">进行中</span></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewMaintenance(3)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" onclick="updateProgress(3)">
                                                    <i class="fas fa-tasks"></i> 进度
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="fw-bold">MNT-2024-003</div>
                                            <small class="text-muted">清洁保养</small>
                                        </td>
                                        <td>预防性维修</td>
                                        <td>2024-04-01</td>
                                        <td>上海某清洁服务公司</td>
                                        <td><span class="cost-highlight">¥3,500</span></td>
                                        <td><span class="maintenance-status-badge status-planned">计划中</span></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewMaintenance(4)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="startMaintenance(4)">
                                                    <i class="fas fa-play"></i> 开始
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="fw-bold">MNT-2024-004</div>
                                            <small class="text-muted">紧急漏水处理</small>
                                        </td>
                                        <td>应急维修</td>
                                        <td>2024-02-28</td>
                                        <td>上海某应急维修队</td>
                                        <td><span class="cost-highlight">¥6,500</span></td>
                                        <td><span class="maintenance-status-badge status-urgent">紧急</span></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewMaintenance(5)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="urgentHandle(5)">
                                                    <i class="fas fa-exclamation-triangle"></i> 紧急处理
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <nav aria-label="维修记录分页">
                            <ul class="pagination justify-content-center">
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1">上一页</a>
                                </li>
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                <li class="page-item">
                                    <a class="page-link" href="#">下一页</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增维修模态框 -->
    <div class="modal fade" id="maintenanceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus"></i> 新增维修记录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="maintenanceForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">维修类型 <span class="text-danger">*</span></label>
                                <select class="form-select" required>
                                    <option value="">请选择维修类型</option>
                                    <option value="1">预防性维修</option>
                                    <option value="2">纠正性维修</option>
                                    <option value="3">应急维修</option>
                                    <option value="4">改善性维修</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">维修日期 <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">维修单位 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">维修费用 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" placeholder="单位：元" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">质保期（年）</label>
                                <input type="number" class="form-control" placeholder="如：2">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">维修状态 <span class="text-danger">*</span></label>
                                <select class="form-select" required>
                                    <option value="">请选择状态</option>
                                    <option value="1">已完成</option>
                                    <option value="2">进行中</option>
                                    <option value="3">计划中</option>
                                    <option value="4">紧急</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">维修内容 <span class="text-danger">*</span></label>
                            <textarea class="form-control" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">使用材料</label>
                            <textarea class="form-control" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">维修报告</label>
                            <input type="file" class="form-control" accept=".pdf,.doc,.docx">
                            <small class="text-muted">支持 PDF、Word 格式</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveMaintenance()">保存维修记录</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 新增维修
        function addMaintenance() {
            const modal = new bootstrap.Modal(document.getElementById('maintenanceModal'));
            modal.show();
        }

        // 查看维修详情
        function viewMaintenance(id) {
            alert('查看维修详情');
        }

        // 编辑维修记录
        function editMaintenance(id) {
            alert('编辑维修记录');
        }

        // 查看质保信息
        function viewWarranty(id) {
            alert('查看质保信息');
        }

        // 更新进度
        function updateProgress(id) {
            alert('更新维修进度');
        }

        // 开始维修
        function startMaintenance(id) {
            if (confirm('确认开始维修？')) {
                alert('维修已开始！');
                location.reload();
            }
        }

        // 紧急处理
        function urgentHandle(id) {
            alert('启动紧急处理流程');
        }

        // 保存维修记录
        function saveMaintenance() {
            alert('维修记录保存成功！');
            bootstrap.Modal.getInstance(document.getElementById('maintenanceModal')).hide();
            location.reload();
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.building-info, .search-filters, .maintenance-table');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
