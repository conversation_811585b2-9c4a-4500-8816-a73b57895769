# 数据库架构图（ERD）

```mermaid
erDiagram
    %% 用户相关表
    USER {
        bigint user_id PK "用户ID"
        varchar username "用户名"
        varchar password "密码"
        varchar email "邮箱"
        varchar phone "手机号"
        varchar real_name "真实姓名"
        int user_type "用户类型"
        int status "状态"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    ROLE {
        bigint role_id PK "角色ID"
        varchar role_name "角色名称"
        varchar role_code "角色编码"
        varchar description "描述"
        int status "状态"
        datetime create_time "创建时间"
    }

    USER_ROLE {
        bigint id PK "主键ID"
        bigint user_id FK "用户ID"
        bigint role_id FK "角色ID"
        datetime create_time "创建时间"
    }

    PERMISSION {
        bigint permission_id PK "权限ID"
        varchar permission_name "权限名称"
        varchar permission_code "权限编码"
        varchar resource_type "资源类型"
        varchar resource_url "资源URL"
        bigint parent_id "父权限ID"
        int sort_order "排序"
        int status "状态"
    }

    ROLE_PERMISSION {
        bigint id PK "主键ID"
        bigint role_id FK "角色ID"
        bigint permission_id FK "权限ID"
        datetime create_time "创建时间"
    }

    %% 企业相关表
    COMPANY {
        bigint company_id PK "企业ID"
        varchar company_name "企业名称"
        varchar company_code "企业编码"
        varchar legal_person "法定代表人"
        varchar business_license "营业执照号"
        varchar company_type "企业类型"
        varchar address "企业地址"
        varchar contact_person "联系人"
        varchar contact_phone "联系电话"
        varchar contact_email "联系邮箱"
        text description "企业描述"
        int status "状态"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
        bigint create_user_id FK "创建用户ID"
    }

    COMPANY_QUALIFICATION {
        bigint qualification_id PK "资质ID"
        bigint company_id FK "企业ID"
        varchar qualification_name "资质名称"
        varchar qualification_code "资质编号"
        varchar qualification_level "资质等级"
        varchar issuing_authority "发证机关"
        date issue_date "发证日期"
        date expire_date "到期日期"
        varchar certificate_file "证书文件"
        int status "状态"
        datetime create_time "创建时间"
    }

    COMPANY_PERSONNEL {
        bigint personnel_id PK "人员ID"
        bigint company_id FK "企业ID"
        varchar name "姓名"
        varchar position "职位"
        varchar id_card "身份证号"
        varchar phone "联系电话"
        varchar email "邮箱"
        varchar qualification "专业资质"
        text experience "工作经验"
        int status "状态"
        datetime create_time "创建时间"
    }

    COMPANY_PERFORMANCE {
        bigint performance_id PK "业绩ID"
        bigint company_id FK "企业ID"
        varchar project_name "项目名称"
        varchar project_location "项目地点"
        decimal project_amount "项目金额"
        date start_date "开始日期"
        date end_date "结束日期"
        varchar project_type "项目类型"
        text project_description "项目描述"
        varchar certificate_file "证明文件"
        datetime create_time "创建时间"
    }

    %% 项目相关表
    PROJECT {
        bigint project_id PK "项目ID"
        varchar project_name "项目名称"
        varchar project_code "项目编号"
        varchar project_location "项目地点"
        varchar owner_name "业主单位"
        varchar design_company "设计单位"
        varchar construction_company "施工单位"
        varchar supervision_company "监理单位"
        decimal project_area "项目面积"
        decimal curtain_wall_area "幕墙面积"
        varchar curtain_wall_type "幕墙类型"
        date start_date "开工日期"
        date planned_completion_date "计划竣工日期"
        date actual_completion_date "实际竣工日期"
        int project_status "项目状态"
        text description "项目描述"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
        bigint create_user_id FK "创建用户ID"
    }

    PROJECT_PROGRESS {
        bigint progress_id PK "进度ID"
        bigint project_id FK "项目ID"
        varchar progress_name "进度节点"
        varchar progress_description "进度描述"
        decimal completion_rate "完成率"
        date planned_date "计划日期"
        date actual_date "实际日期"
        int status "状态"
        varchar remark "备注"
        datetime create_time "创建时间"
        bigint create_user_id FK "创建用户ID"
    }

    PROJECT_QUALITY {
        bigint quality_id PK "质量检查ID"
        bigint project_id FK "项目ID"
        varchar check_item "检查项目"
        varchar check_standard "检查标准"
        varchar check_result "检查结果"
        varchar check_person "检查人员"
        date check_date "检查日期"
        varchar problem_description "问题描述"
        varchar rectification_measures "整改措施"
        date rectification_deadline "整改期限"
        int rectification_status "整改状态"
        varchar attachment "附件"
        datetime create_time "创建时间"
    }

    %% 既有幕墙相关表
    EXISTING_CURTAIN_WALL {
        bigint curtain_wall_id PK "既有幕墙ID"
        varchar building_name "建筑名称"
        varchar building_address "建筑地址"
        varchar owner_name "业主单位"
        varchar property_company "物业公司"
        date construction_date "建成日期"
        varchar original_constructor "原施工单位"
        varchar curtain_wall_type "幕墙类型"
        varchar frame_material "框架材料"
        varchar glass_type "玻璃类型"
        decimal total_area "总面积"
        int floor_count "楼层数"
        varchar structural_form "结构形式"
        text technical_parameters "技术参数"
        int safety_level "安全等级"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    CURTAIN_WALL_INSPECTION {
        bigint inspection_id PK "检查ID"
        bigint curtain_wall_id FK "既有幕墙ID"
        bigint inspection_company_id FK "检查企业ID"
        varchar inspection_type "检查类型"
        date inspection_date "检查日期"
        varchar inspector "检查人员"
        varchar inspection_standard "检查标准"
        text inspection_content "检查内容"
        varchar safety_assessment "安全评估"
        text problems_found "发现问题"
        text recommendations "建议措施"
        varchar inspection_report "检查报告"
        int status "状态"
        datetime create_time "创建时间"
    }

    CURTAIN_WALL_MAINTENANCE {
        bigint maintenance_id PK "维修ID"
        bigint curtain_wall_id FK "既有幕墙ID"
        bigint maintenance_company_id FK "维修企业ID"
        varchar maintenance_type "维修类型"
        text problem_description "问题描述"
        text maintenance_plan "维修方案"
        date planned_start_date "计划开始日期"
        date planned_end_date "计划结束日期"
        date actual_start_date "实际开始日期"
        date actual_end_date "实际结束日期"
        decimal maintenance_cost "维修费用"
        varchar materials_used "使用材料"
        text maintenance_process "维修过程"
        varchar quality_acceptance "质量验收"
        varchar warranty_period "保修期"
        int status "状态"
        datetime create_time "创建时间"
    }

    %% 协会管理相关表
    ASSOCIATION_MEMBER {
        bigint member_id PK "会员ID"
        bigint company_id FK "企业ID"
        varchar member_type "会员类型"
        date join_date "入会日期"
        varchar member_level "会员等级"
        decimal annual_fee "年费"
        date fee_paid_date "缴费日期"
        date fee_expire_date "费用到期日期"
        int payment_status "缴费状态"
        text member_benefits "会员权益"
        int status "状态"
        datetime create_time "创建时间"
    }

    INDUSTRY_STANDARD {
        bigint standard_id PK "标准ID"
        varchar standard_name "标准名称"
        varchar standard_code "标准编号"
        varchar standard_type "标准类型"
        varchar issuing_authority "发布机构"
        date issue_date "发布日期"
        date effective_date "生效日期"
        varchar standard_file "标准文件"
        text standard_summary "标准摘要"
        int status "状态"
        datetime create_time "创建时间"
    }

    CREDIT_EVALUATION {
        bigint evaluation_id PK "评价ID"
        bigint company_id FK "企业ID"
        varchar evaluation_period "评价期间"
        int credit_score "信用分数"
        varchar credit_level "信用等级"
        text evaluation_basis "评价依据"
        text positive_records "正面记录"
        text negative_records "负面记录"
        varchar evaluator "评价人员"
        date evaluation_date "评价日期"
        int status "状态"
        datetime create_time "创建时间"
    }

    %% 系统管理相关表
    SYSTEM_CONFIG {
        bigint config_id PK "配置ID"
        varchar config_key "配置键"
        varchar config_value "配置值"
        varchar config_name "配置名称"
        varchar config_group "配置分组"
        text description "描述"
        int sort_order "排序"
        int status "状态"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    OPERATION_LOG {
        bigint log_id PK "日志ID"
        bigint user_id FK "用户ID"
        varchar operation_type "操作类型"
        varchar operation_name "操作名称"
        varchar request_method "请求方法"
        varchar request_url "请求URL"
        text request_params "请求参数"
        varchar response_result "响应结果"
        varchar ip_address "IP地址"
        varchar user_agent "用户代理"
        int execution_time "执行时间"
        datetime create_time "创建时间"
    }

    DATA_DICTIONARY {
        bigint dict_id PK "字典ID"
        varchar dict_type "字典类型"
        varchar dict_code "字典编码"
        varchar dict_label "字典标签"
        varchar dict_value "字典值"
        int sort_order "排序"
        varchar css_class "CSS类名"
        varchar list_class "列表类名"
        int is_default "是否默认"
        int status "状态"
        datetime create_time "创建时间"
    }

    %% 关系定义
    USER ||--o{ USER_ROLE : "用户角色关联"
    ROLE ||--o{ USER_ROLE : "角色用户关联"
    ROLE ||--o{ ROLE_PERMISSION : "角色权限关联"
    PERMISSION ||--o{ ROLE_PERMISSION : "权限角色关联"
    
    USER ||--o{ COMPANY : "用户创建企业"
    COMPANY ||--o{ COMPANY_QUALIFICATION : "企业资质"
    COMPANY ||--o{ COMPANY_PERSONNEL : "企业人员"
    COMPANY ||--o{ COMPANY_PERFORMANCE : "企业业绩"
    COMPANY ||--o{ ASSOCIATION_MEMBER : "协会会员"
    COMPANY ||--o{ CREDIT_EVALUATION : "信用评价"
    
    USER ||--o{ PROJECT : "用户创建项目"
    PROJECT ||--o{ PROJECT_PROGRESS : "项目进度"
    PROJECT ||--o{ PROJECT_QUALITY : "项目质量"
    
    EXISTING_CURTAIN_WALL ||--o{ CURTAIN_WALL_INSPECTION : "幕墙检查"
    EXISTING_CURTAIN_WALL ||--o{ CURTAIN_WALL_MAINTENANCE : "幕墙维修"
    COMPANY ||--o{ CURTAIN_WALL_INSPECTION : "检查企业"
    COMPANY ||--o{ CURTAIN_WALL_MAINTENANCE : "维修企业"
    
    USER ||--o{ OPERATION_LOG : "操作日志"
```

## 数据库设计说明

### 1. 用户权限模块
- **USER**: 用户基础信息表
- **ROLE**: 角色定义表
- **PERMISSION**: 权限定义表
- **USER_ROLE**: 用户角色关联表
- **ROLE_PERMISSION**: 角色权限关联表

### 2. 企业管理模块
- **COMPANY**: 企业基础信息表
- **COMPANY_QUALIFICATION**: 企业资质表
- **COMPANY_PERSONNEL**: 企业人员表
- **COMPANY_PERFORMANCE**: 企业业绩表

### 3. 项目管理模块
- **PROJECT**: 项目基础信息表
- **PROJECT_PROGRESS**: 项目进度表
- **PROJECT_QUALITY**: 项目质量检查表

### 4. 既有幕墙管理模块
- **EXISTING_CURTAIN_WALL**: 既有幕墙信息表
- **CURTAIN_WALL_INSPECTION**: 幕墙检查记录表
- **CURTAIN_WALL_MAINTENANCE**: 幕墙维修记录表

### 5. 协会管理模块
- **ASSOCIATION_MEMBER**: 协会会员表
- **INDUSTRY_STANDARD**: 行业标准表
- **CREDIT_EVALUATION**: 信用评价表

### 6. 系统管理模块
- **SYSTEM_CONFIG**: 系统配置表
- **OPERATION_LOG**: 操作日志表
- **DATA_DICTIONARY**: 数据字典表

## 数据库设计原则

1. **规范化设计**: 遵循第三范式，减少数据冗余
2. **索引优化**: 为查询频繁的字段建立索引
3. **数据完整性**: 使用外键约束保证数据一致性
4. **扩展性**: 预留扩展字段，支持业务发展
5. **安全性**: 敏感数据加密存储，操作日志记录
