<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目列表 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar {
            background: linear-gradient(45deg, #667eea, #764ba2);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 0;
        }
        .sidebar-menu {
            list-style: none;
            padding: 1rem 0;
            margin: 0;
        }
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        .sidebar-menu a {
            display: block;
            padding: 0.8rem 1.5rem;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background: rgba(102, 126, 234, 0.1);
            border-left-color: #667eea;
            color: #667eea;
        }
        .sidebar-menu i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            padding: 2rem;
        }
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .search-filters {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .project-table {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 8px;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .project-status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .status-planning { background: rgba(108, 117, 125, 0.1); color: #6c757d; }
        .status-construction { background: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .status-completed { background: rgba(40, 167, 69, 0.1); color: #28a745; }
        .status-accepted { background: rgba(102, 126, 234, 0.1); color: #667eea; }
        .table th {
            border-top: none;
            font-weight: 600;
            color: #333;
            background: #f8f9fa;
        }
        .action-buttons .btn {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        .stats-cards {
            margin-bottom: 2rem;
        }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        .stats-label {
            color: #666;
            font-size: 0.9rem;
        }
        .progress-bar-container {
            width: 100px;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -250px;
                width: 250px;
                z-index: 1000;
                transition: left 0.3s ease;
            }
            .sidebar.show {
                left: 0;
            }
            .main-content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <button class="navbar-toggler d-lg-none" type="button" onclick="toggleSidebar()">
                <span class="navbar-toggler-icon"></span>
            </button>
            <a class="navbar-brand" href="../dashboard.html">
                <i class="fas fa-building"></i> 幕墙信息化平台
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user"></i> 个人资料</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-cog"></i> 系统设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar" id="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="../company/list.html"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="list.html" class="active"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="../curtain-wall/list.html"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="../statistics/overview.html"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="../system/users.html"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2><i class="fas fa-project-diagram"></i> 项目管理</h2>
                                <p class="text-muted mb-0">管理幕墙工程项目信息</p>
                            </div>
                            <div>
                                <button class="btn btn-primary" onclick="window.location.href='edit.html'">
                                    <i class="fas fa-plus"></i> 新增项目
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 统计卡片 -->
                    <div class="row stats-cards">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-primary">89</div>
                                <div class="stats-label">项目总数</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-warning">45</div>
                                <div class="stats-label">施工中</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-success">32</div>
                                <div class="stats-label">已完工</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-info">12</div>
                                <div class="stats-label">已验收</div>
                            </div>
                        </div>
                    </div>

                    <!-- 搜索筛选 -->
                    <div class="search-filters">
                        <form class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">项目名称</label>
                                <input type="text" class="form-control" placeholder="请输入项目名称">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">项目状态</label>
                                <select class="form-select">
                                    <option value="">全部状态</option>
                                    <option value="1">规划中</option>
                                    <option value="2">施工中</option>
                                    <option value="3">已完工</option>
                                    <option value="4">已验收</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">地区</label>
                                <select class="form-select">
                                    <option value="">全部地区</option>
                                    <option value="pudong">浦东新区</option>
                                    <option value="huangpu">黄浦区</option>
                                    <option value="jingan">静安区</option>
                                    <option value="xuhui">徐汇区</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">时间范围</label>
                                <select class="form-select">
                                    <option value="">全部时间</option>
                                    <option value="2024">2024年</option>
                                    <option value="2023">2023年</option>
                                    <option value="2022">2022年</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    <button type="reset" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo"></i> 重置
                                    </button>
                                    <button type="button" class="btn btn-outline-success" onclick="exportData()">
                                        <i class="fas fa-download"></i> 导出
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 项目列表 -->
                    <div class="project-table">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>项目名称</th>
                                        <th>项目地点</th>
                                        <th>施工单位</th>
                                        <th>幕墙面积</th>
                                        <th>项目状态</th>
                                        <th>进度</th>
                                        <th>开工日期</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-building fa-2x text-primary"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">上海某商业大厦幕墙工程</div>
                                                    <small class="text-muted">PJ001</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>上海市黄浦区</td>
                                        <td>上海某建筑幕墙工程有限公司</td>
                                        <td>15,000㎡</td>
                                        <td><span class="project-status-badge status-construction">施工中</span></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress-bar-container me-2">
                                                    <div class="progress-bar" style="width: 65%"></div>
                                                </div>
                                                <small>65%</small>
                                            </div>
                                        </td>
                                        <td>2024-01-15</td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewProject(1)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="editProject(1)">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </button>
                                                <button class="btn btn-sm btn-outline-info" onclick="manageProgress(1)">
                                                    <i class="fas fa-tasks"></i> 进度
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-building fa-2x text-success"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">上海某写字楼幕墙改造工程</div>
                                                    <small class="text-muted">PJ002</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>上海市浦东新区</td>
                                        <td>上海某幕墙维修服务有限公司</td>
                                        <td>8,500㎡</td>
                                        <td><span class="project-status-badge status-completed">已完工</span></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress-bar-container me-2">
                                                    <div class="progress-bar" style="width: 100%"></div>
                                                </div>
                                                <small>100%</small>
                                            </div>
                                        </td>
                                        <td>2023-08-01</td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewProject(2)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" onclick="qualityCheck(2)">
                                                    <i class="fas fa-check-circle"></i> 质检
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="acceptProject(2)">
                                                    <i class="fas fa-clipboard-check"></i> 验收
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-building fa-2x text-warning"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">上海某酒店幕墙工程</div>
                                                    <small class="text-muted">PJ003</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>上海市静安区</td>
                                        <td>上海某建筑幕墙工程有限公司</td>
                                        <td>12,000㎡</td>
                                        <td><span class="project-status-badge status-construction">施工中</span></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress-bar-container me-2">
                                                    <div class="progress-bar" style="width: 35%"></div>
                                                </div>
                                                <small>35%</small>
                                            </div>
                                        </td>
                                        <td>2024-02-01</td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewProject(3)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="editProject(3)">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </button>
                                                <button class="btn btn-sm btn-outline-info" onclick="manageProgress(3)">
                                                    <i class="fas fa-tasks"></i> 进度
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-building fa-2x text-info"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">上海某办公楼幕墙工程</div>
                                                    <small class="text-muted">PJ004</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>上海市徐汇区</td>
                                        <td>上海某建筑幕墙工程有限公司</td>
                                        <td>20,000㎡</td>
                                        <td><span class="project-status-badge status-accepted">已验收</span></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress-bar-container me-2">
                                                    <div class="progress-bar" style="width: 100%"></div>
                                                </div>
                                                <small>100%</small>
                                            </div>
                                        </td>
                                        <td>2023-05-01</td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewProject(4)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-info" onclick="viewReport(4)">
                                                    <i class="fas fa-file-alt"></i> 报告
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary" onclick="archiveProject(4)">
                                                    <i class="fas fa-archive"></i> 归档
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <nav aria-label="项目列表分页">
                            <ul class="pagination justify-content-center">
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1">上一页</a>
                                </li>
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                <li class="page-item"><a class="page-link" href="#">3</a></li>
                                <li class="page-item">
                                    <a class="page-link" href="#">下一页</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 切换侧边栏
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('show');
        }

        // 查看项目详情
        function viewProject(id) {
            window.location.href = `detail.html?id=${id}`;
        }

        // 编辑项目
        function editProject(id) {
            window.location.href = `edit.html?id=${id}`;
        }

        // 管理项目进度
        function manageProgress(id) {
            window.location.href = `progress.html?id=${id}`;
        }

        // 质量检查
        function qualityCheck(id) {
            alert('进入质量检查页面');
        }

        // 项目验收
        function acceptProject(id) {
            if (confirm('确认该项目已完成验收？')) {
                alert('项目验收完成！');
                location.reload();
            }
        }

        // 查看报告
        function viewReport(id) {
            alert('查看项目报告');
        }

        // 归档项目
        function archiveProject(id) {
            if (confirm('确认归档该项目？')) {
                alert('项目已归档！');
                location.reload();
            }
        }

        // 导出数据
        function exportData() {
            alert('正在导出项目数据...');
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stats-card, .search-filters, .project-table');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
