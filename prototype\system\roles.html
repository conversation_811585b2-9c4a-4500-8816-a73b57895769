<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色管理 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Microsoft YaHei', sans-serif; }
        .navbar { background: linear-gradient(45deg, #667eea, #764ba2); box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .sidebar { background: white; min-height: calc(100vh - 76px); box-shadow: 2px 0 10px rgba(0,0,0,0.1); padding: 0; }
        .sidebar-menu { list-style: none; padding: 1rem 0; margin: 0; }
        .sidebar-menu li { margin-bottom: 0.5rem; }
        .sidebar-menu a { display: block; padding: 0.8rem 1.5rem; color: #333; text-decoration: none; transition: all 0.3s ease; border-left: 3px solid transparent; }
        .sidebar-menu a:hover, .sidebar-menu a.active { background: rgba(102, 126, 234, 0.1); border-left-color: #667eea; color: #667eea; }
        .sidebar-menu i { width: 20px; margin-right: 10px; }
        .main-content { padding: 2rem; }
        .page-header { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .role-card { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); transition: transform 0.3s ease; }
        .role-card:hover { transform: translateY(-5px); }
        .role-icon { width: 60px; height: 60px; border-radius: 15px; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; color: white; margin-bottom: 1rem; }
        .role-title { font-size: 1.2rem; font-weight: 600; margin-bottom: 0.5rem; }
        .role-description { color: #666; font-size: 0.9rem; margin-bottom: 1rem; }
        .role-stats { display: flex; justify-content: space-between; margin-bottom: 1rem; }
        .role-stat { text-align: center; }
        .role-stat-number { font-size: 1.5rem; font-weight: 700; color: #667eea; }
        .role-stat-label { font-size: 0.8rem; color: #666; }
        .btn-primary { background: linear-gradient(45deg, #667eea, #764ba2); border: none; border-radius: 8px; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3); }
        .permission-group { background: #f8f9fa; border-radius: 10px; padding: 1rem; margin-bottom: 1rem; }
        .permission-group-title { font-weight: 600; margin-bottom: 0.5rem; color: #333; }
        .permission-item { display: flex; align-items: center; margin-bottom: 0.5rem; }
        .permission-item:last-child { margin-bottom: 0; }
        .permission-checkbox { margin-right: 0.5rem; }
        .permission-label { font-size: 0.9rem; color: #666; }
        .role-badge { padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.8rem; font-weight: 500; }
        .role-admin { background: rgba(220, 53, 69, 0.1); color: #dc3545; }
        .role-company { background: rgba(102, 126, 234, 0.1); color: #667eea; }
        .role-inspector { background: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .role-viewer { background: rgba(108, 117, 125, 0.1); color: #6c757d; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.html"><i class="fas fa-building"></i> 幕墙信息化平台</a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="../company/list.html"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="../project/list.html"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="../curtain-wall/list.html"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="../statistics/overview.html"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="users.html" class="active"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb mb-2">
                                        <li class="breadcrumb-item"><a href="users.html">系统管理</a></li>
                                        <li class="breadcrumb-item active">角色管理</li>
                                    </ol>
                                </nav>
                                <h2><i class="fas fa-user-tag"></i> 角色管理</h2>
                                <p class="text-muted mb-0">管理系统角色和权限配置</p>
                            </div>
                            <div>
                                <button class="btn btn-outline-primary me-2" onclick="window.location.href='users.html'">
                                    <i class="fas fa-users"></i> 用户管理
                                </button>
                                <button class="btn btn-primary" onclick="addRole()">
                                    <i class="fas fa-plus"></i> 新增角色
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 角色卡片 -->
                    <div class="row">
                        <!-- 系统管理员 -->
                        <div class="col-lg-6 col-xl-4">
                            <div class="role-card">
                                <div class="role-icon" style="background: linear-gradient(45deg, #dc3545, #e83e8c);">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="role-title">系统管理员</div>
                                <div class="role-description">拥有系统所有功能的完整访问权限，负责系统维护和用户管理</div>
                                <div class="role-stats">
                                    <div class="role-stat">
                                        <div class="role-stat-number">2</div>
                                        <div class="role-stat-label">用户数量</div>
                                    </div>
                                    <div class="role-stat">
                                        <div class="role-stat-number">100%</div>
                                        <div class="role-stat-label">权限覆盖</div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="role-badge role-admin">管理员</span>
                                    <div>
                                        <button class="btn btn-sm btn-outline-primary me-2" onclick="viewRole(1)">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="editRole(1)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 企业用户 -->
                        <div class="col-lg-6 col-xl-4">
                            <div class="role-card">
                                <div class="role-icon" style="background: linear-gradient(45deg, #667eea, #764ba2);">
                                    <i class="fas fa-industry"></i>
                                </div>
                                <div class="role-title">企业用户</div>
                                <div class="role-description">企业注册用户，可以管理本企业信息、项目信息和资质证书</div>
                                <div class="role-stats">
                                    <div class="role-stat">
                                        <div class="role-stat-number">45</div>
                                        <div class="role-stat-label">用户数量</div>
                                    </div>
                                    <div class="role-stat">
                                        <div class="role-stat-number">65%</div>
                                        <div class="role-stat-label">权限覆盖</div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="role-badge role-company">企业用户</span>
                                    <div>
                                        <button class="btn btn-sm btn-outline-primary me-2" onclick="viewRole(2)">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="editRole(2)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 检查员 -->
                        <div class="col-lg-6 col-xl-4">
                            <div class="role-card">
                                <div class="role-icon" style="background: linear-gradient(45deg, #ffc107, #fd7e14);">
                                    <i class="fas fa-search"></i>
                                </div>
                                <div class="role-title">检查员</div>
                                <div class="role-description">专业检查人员，负责既有幕墙安全检查和质量评估</div>
                                <div class="role-stats">
                                    <div class="role-stat">
                                        <div class="role-stat-number">18</div>
                                        <div class="role-stat-label">用户数量</div>
                                    </div>
                                    <div class="role-stat">
                                        <div class="role-stat-number">45%</div>
                                        <div class="role-stat-label">权限覆盖</div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="role-badge role-inspector">检查员</span>
                                    <div>
                                        <button class="btn btn-sm btn-outline-primary me-2" onclick="viewRole(3)">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="editRole(3)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 访客用户 -->
                        <div class="col-lg-6 col-xl-4">
                            <div class="role-card">
                                <div class="role-icon" style="background: linear-gradient(45deg, #6c757d, #adb5bd);">
                                    <i class="fas fa-eye"></i>
                                </div>
                                <div class="role-title">访客用户</div>
                                <div class="role-description">只读权限用户，可以查看公开信息和统计数据</div>
                                <div class="role-stats">
                                    <div class="role-stat">
                                        <div class="role-stat-number">3</div>
                                        <div class="role-stat-label">用户数量</div>
                                    </div>
                                    <div class="role-stat">
                                        <div class="role-stat-number">25%</div>
                                        <div class="role-stat-label">权限覆盖</div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="role-badge role-viewer">访客</span>
                                    <div>
                                        <button class="btn btn-sm btn-outline-primary me-2" onclick="viewRole(4)">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="editRole(4)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 角色详情/编辑模态框 -->
    <div class="modal fade" id="roleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-tag"></i> <span id="modalTitle">角色详情</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="roleForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">角色名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="roleName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">角色代码 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="roleCode" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">角色描述</label>
                            <textarea class="form-control" id="roleDescription" rows="3"></textarea>
                        </div>
                        
                        <h6>权限配置</h6>
                        
                        <!-- 企业管理权限 -->
                        <div class="permission-group">
                            <div class="permission-group-title">
                                <i class="fas fa-industry"></i> 企业管理
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" class="permission-checkbox" id="perm_company_view">
                                <label class="permission-label">查看企业信息</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" class="permission-checkbox" id="perm_company_edit">
                                <label class="permission-label">编辑企业信息</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" class="permission-checkbox" id="perm_company_delete">
                                <label class="permission-label">删除企业信息</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" class="permission-checkbox" id="perm_qualification_manage">
                                <label class="permission-label">管理企业资质</label>
                            </div>
                        </div>

                        <!-- 项目管理权限 -->
                        <div class="permission-group">
                            <div class="permission-group-title">
                                <i class="fas fa-project-diagram"></i> 项目管理
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" class="permission-checkbox" id="perm_project_view">
                                <label class="permission-label">查看项目信息</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" class="permission-checkbox" id="perm_project_edit">
                                <label class="permission-label">编辑项目信息</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" class="permission-checkbox" id="perm_project_progress">
                                <label class="permission-label">管理项目进度</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" class="permission-checkbox" id="perm_project_quality">
                                <label class="permission-label">质量检查管理</label>
                            </div>
                        </div>

                        <!-- 既有幕墙权限 -->
                        <div class="permission-group">
                            <div class="permission-group-title">
                                <i class="fas fa-building-columns"></i> 既有幕墙
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" class="permission-checkbox" id="perm_curtain_view">
                                <label class="permission-label">查看幕墙信息</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" class="permission-checkbox" id="perm_curtain_inspect">
                                <label class="permission-label">安全检查管理</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" class="permission-checkbox" id="perm_curtain_maintain">
                                <label class="permission-label">维修记录管理</label>
                            </div>
                        </div>

                        <!-- 系统管理权限 -->
                        <div class="permission-group">
                            <div class="permission-group-title">
                                <i class="fas fa-cog"></i> 系统管理
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" class="permission-checkbox" id="perm_user_manage">
                                <label class="permission-label">用户管理</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" class="permission-checkbox" id="perm_role_manage">
                                <label class="permission-label">角色管理</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" class="permission-checkbox" id="perm_system_config">
                                <label class="permission-label">系统配置</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" class="permission-checkbox" id="perm_statistics_view">
                                <label class="permission-label">统计分析</label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveRole()" id="saveBtn" style="display: none;">保存角色</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 新增角色
        function addRole() {
            document.getElementById('modalTitle').textContent = '新增角色';
            document.getElementById('roleForm').reset();
            document.getElementById('saveBtn').style.display = 'block';
            const modal = new bootstrap.Modal(document.getElementById('roleModal'));
            modal.show();
        }

        // 查看角色
        function viewRole(id) {
            document.getElementById('modalTitle').textContent = '角色详情';
            document.getElementById('saveBtn').style.display = 'none';
            
            // 模拟加载角色数据
            if (id === 1) {
                document.getElementById('roleName').value = '系统管理员';
                document.getElementById('roleCode').value = 'admin';
                document.getElementById('roleDescription').value = '拥有系统所有功能的完整访问权限';
                // 选中所有权限
                document.querySelectorAll('.permission-checkbox').forEach(cb => cb.checked = true);
            } else if (id === 2) {
                document.getElementById('roleName').value = '企业用户';
                document.getElementById('roleCode').value = 'company';
                document.getElementById('roleDescription').value = '企业注册用户，可以管理本企业信息';
                // 选中部分权限
                ['perm_company_view', 'perm_company_edit', 'perm_project_view', 'perm_project_edit'].forEach(id => {
                    document.getElementById(id).checked = true;
                });
            }
            
            // 禁用所有输入
            document.querySelectorAll('#roleForm input, #roleForm textarea').forEach(el => el.disabled = true);
            
            const modal = new bootstrap.Modal(document.getElementById('roleModal'));
            modal.show();
        }

        // 编辑角色
        function editRole(id) {
            viewRole(id); // 先加载数据
            document.getElementById('modalTitle').textContent = '编辑角色';
            document.getElementById('saveBtn').style.display = 'block';
            
            // 启用所有输入
            document.querySelectorAll('#roleForm input, #roleForm textarea').forEach(el => el.disabled = false);
        }

        // 保存角色
        function saveRole() {
            alert('角色信息保存成功！');
            bootstrap.Modal.getInstance(document.getElementById('roleModal')).hide();
            location.reload();
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.role-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
