<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Microsoft YaHei', sans-serif; }
        .navbar { background: linear-gradient(45deg, #667eea, #764ba2); box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .sidebar { background: white; min-height: calc(100vh - 76px); box-shadow: 2px 0 10px rgba(0,0,0,0.1); padding: 0; }
        .sidebar-menu { list-style: none; padding: 1rem 0; margin: 0; }
        .sidebar-menu li { margin-bottom: 0.5rem; }
        .sidebar-menu a { display: block; padding: 0.8rem 1.5rem; color: #333; text-decoration: none; transition: all 0.3s ease; border-left: 3px solid transparent; }
        .sidebar-menu a:hover, .sidebar-menu a.active { background: rgba(102, 126, 234, 0.1); border-left-color: #667eea; color: #667eea; }
        .sidebar-menu i { width: 20px; margin-right: 10px; }
        .main-content { padding: 2rem; }
        .page-header { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .settings-card { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .settings-section { margin-bottom: 2rem; padding-bottom: 2rem; border-bottom: 1px solid #e9ecef; }
        .settings-section:last-child { border-bottom: none; margin-bottom: 0; }
        .section-title { font-size: 1.2rem; font-weight: 600; color: #333; margin-bottom: 1.5rem; padding-left: 1rem; border-left: 4px solid #667eea; }
        .form-control, .form-select { border-radius: 8px; border: 2px solid #e9ecef; padding: 0.8rem 1rem; transition: all 0.3s ease; }
        .form-control:focus, .form-select:focus { border-color: #667eea; box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25); }
        .btn-primary { background: linear-gradient(45deg, #667eea, #764ba2); border: none; border-radius: 8px; padding: 0.8rem 2rem; font-weight: 600; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3); }
        .setting-item { display: flex; justify-content: space-between; align-items: center; padding: 1rem; background: #f8f9fa; border-radius: 10px; margin-bottom: 1rem; }
        .setting-label { font-weight: 500; color: #333; }
        .setting-description { font-size: 0.9rem; color: #666; margin-top: 0.3rem; }
        .switch { position: relative; display: inline-block; width: 60px; height: 34px; }
        .switch input { opacity: 0; width: 0; height: 0; }
        .slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #ccc; transition: .4s; border-radius: 34px; }
        .slider:before { position: absolute; content: ""; height: 26px; width: 26px; left: 4px; bottom: 4px; background-color: white; transition: .4s; border-radius: 50%; }
        input:checked + .slider { background-color: #667eea; }
        input:checked + .slider:before { transform: translateX(26px); }
        .backup-item { display: flex; justify-content: space-between; align-items: center; padding: 1rem; background: #f8f9fa; border-radius: 10px; margin-bottom: 1rem; }
        .backup-info { flex: 1; }
        .backup-date { font-weight: 500; color: #333; }
        .backup-size { font-size: 0.9rem; color: #666; }
        .log-item { padding: 0.8rem; background: #f8f9fa; border-radius: 8px; margin-bottom: 0.5rem; font-family: 'Courier New', monospace; font-size: 0.9rem; }
        .log-level-info { border-left: 4px solid #17a2b8; }
        .log-level-warning { border-left: 4px solid #ffc107; }
        .log-level-error { border-left: 4px solid #dc3545; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.html"><i class="fas fa-building"></i> 幕墙信息化平台</a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="../company/list.html"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="../project/list.html"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="../curtain-wall/list.html"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="../statistics/overview.html"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="users.html" class="active"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb mb-2">
                                        <li class="breadcrumb-item"><a href="users.html">系统管理</a></li>
                                        <li class="breadcrumb-item active">系统设置</li>
                                    </ol>
                                </nav>
                                <h2><i class="fas fa-cog"></i> 系统设置</h2>
                                <p class="text-muted mb-0">配置系统参数和功能选项</p>
                            </div>
                            <div>
                                <button class="btn btn-outline-primary me-2" onclick="window.location.href='users.html'">
                                    <i class="fas fa-users"></i> 用户管理
                                </button>
                                <button class="btn btn-primary" onclick="saveAllSettings()">
                                    <i class="fas fa-save"></i> 保存所有设置
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 系统设置 -->
                    <div class="settings-card">
                        <!-- 基本设置 -->
                        <div class="settings-section">
                            <h4 class="section-title">基本设置</h4>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">系统名称</label>
                                    <input type="text" class="form-control" value="幕墙信息化平台">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">系统版本</label>
                                    <input type="text" class="form-control" value="v1.0.0" readonly>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">管理员邮箱</label>
                                    <input type="email" class="form-control" value="<EMAIL>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">系统时区</label>
                                    <select class="form-select">
                                        <option value="Asia/Shanghai" selected>Asia/Shanghai (UTC+8)</option>
                                        <option value="UTC">UTC (UTC+0)</option>
                                        <option value="America/New_York">America/New_York (UTC-5)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">系统描述</label>
                                <textarea class="form-control" rows="3">上海市装饰装修行业协会幕墙信息化管理平台，用于管理幕墙企业、项目和既有幕墙安全信息。</textarea>
                            </div>
                        </div>

                        <!-- 功能开关 -->
                        <div class="settings-section">
                            <h4 class="section-title">功能开关</h4>
                            <div class="setting-item">
                                <div>
                                    <div class="setting-label">用户注册</div>
                                    <div class="setting-description">允许新用户自主注册账号</div>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="setting-item">
                                <div>
                                    <div class="setting-label">邮件通知</div>
                                    <div class="setting-description">系统事件邮件通知功能</div>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="setting-item">
                                <div>
                                    <div class="setting-label">短信通知</div>
                                    <div class="setting-description">重要事件短信通知功能</div>
                                </div>
                                <label class="switch">
                                    <input type="checkbox">
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="setting-item">
                                <div>
                                    <div class="setting-label">文件上传</div>
                                    <div class="setting-description">允许用户上传文件和图片</div>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="setting-item">
                                <div>
                                    <div class="setting-label">数据导出</div>
                                    <div class="setting-description">允许用户导出数据报表</div>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>

                        <!-- 安全设置 -->
                        <div class="settings-section">
                            <h4 class="section-title">安全设置</h4>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">密码最小长度</label>
                                    <input type="number" class="form-control" value="8" min="6" max="20">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">登录失败锁定次数</label>
                                    <input type="number" class="form-control" value="5" min="3" max="10">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">会话超时时间（分钟）</label>
                                    <input type="number" class="form-control" value="30" min="15" max="480">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">密码有效期（天）</label>
                                    <input type="number" class="form-control" value="90" min="30" max="365">
                                </div>
                            </div>
                        </div>

                        <!-- 文件设置 -->
                        <div class="settings-section">
                            <h4 class="section-title">文件设置</h4>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">单文件最大大小（MB）</label>
                                    <input type="number" class="form-control" value="10" min="1" max="100">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">允许的文件类型</label>
                                    <input type="text" class="form-control" value="pdf,doc,docx,jpg,jpeg,png,gif">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">文件存储路径</label>
                                    <input type="text" class="form-control" value="/uploads/" readonly>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">文件保存天数</label>
                                    <input type="number" class="form-control" value="365" min="30">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据备份 -->
                    <div class="settings-card">
                        <h4 class="section-title">数据备份</h4>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h6>备份列表</h6>
                                <small class="text-muted">系统会自动进行每日备份</small>
                            </div>
                            <button class="btn btn-primary" onclick="createBackup()">
                                <i class="fas fa-plus"></i> 立即备份
                            </button>
                        </div>
                        
                        <div class="backup-item">
                            <div class="backup-info">
                                <div class="backup-date">2024-02-20 02:00:00</div>
                                <div class="backup-size">文件大小: 125.6 MB</div>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-outline-success me-2" onclick="downloadBackup(1)">
                                    <i class="fas fa-download"></i> 下载
                                </button>
                                <button class="btn btn-sm btn-outline-warning me-2" onclick="restoreBackup(1)">
                                    <i class="fas fa-undo"></i> 恢复
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteBackup(1)">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </div>
                        </div>
                        
                        <div class="backup-item">
                            <div class="backup-info">
                                <div class="backup-date">2024-02-19 02:00:00</div>
                                <div class="backup-size">文件大小: 124.2 MB</div>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-outline-success me-2" onclick="downloadBackup(2)">
                                    <i class="fas fa-download"></i> 下载
                                </button>
                                <button class="btn btn-sm btn-outline-warning me-2" onclick="restoreBackup(2)">
                                    <i class="fas fa-undo"></i> 恢复
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteBackup(2)">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 系统日志 -->
                    <div class="settings-card">
                        <h4 class="section-title">系统日志</h4>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h6>最近日志</h6>
                                <small class="text-muted">显示最近50条系统日志</small>
                            </div>
                            <div>
                                <button class="btn btn-outline-secondary me-2" onclick="clearLogs()">
                                    <i class="fas fa-trash"></i> 清空日志
                                </button>
                                <button class="btn btn-primary" onclick="exportLogs()">
                                    <i class="fas fa-download"></i> 导出日志
                                </button>
                            </div>
                        </div>
                        
                        <div class="log-item log-level-info">
                            <strong>[INFO]</strong> 2024-02-20 10:30:15 - 用户 admin 登录系统
                        </div>
                        <div class="log-item log-level-info">
                            <strong>[INFO]</strong> 2024-02-20 10:25:32 - 企业信息更新成功 (ID: 123)
                        </div>
                        <div class="log-item log-level-warning">
                            <strong>[WARN]</strong> 2024-02-20 10:20:18 - 用户 company001 登录失败
                        </div>
                        <div class="log-item log-level-info">
                            <strong>[INFO]</strong> 2024-02-20 10:15:45 - 系统备份完成
                        </div>
                        <div class="log-item log-level-error">
                            <strong>[ERROR]</strong> 2024-02-20 10:10:22 - 文件上传失败: 文件大小超限
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 保存所有设置
        function saveAllSettings() {
            if (confirm('确认保存所有系统设置？')) {
                alert('系统设置保存成功！');
            }
        }

        // 创建备份
        function createBackup() {
            if (confirm('确认立即创建数据备份？')) {
                alert('数据备份已开始，请稍候...');
                setTimeout(() => {
                    alert('数据备份完成！');
                    location.reload();
                }, 2000);
            }
        }

        // 下载备份
        function downloadBackup(id) {
            alert('正在下载备份文件...');
        }

        // 恢复备份
        function restoreBackup(id) {
            if (confirm('确认恢复此备份？此操作将覆盖当前数据！')) {
                alert('数据恢复已开始，请稍候...');
            }
        }

        // 删除备份
        function deleteBackup(id) {
            if (confirm('确认删除此备份文件？')) {
                alert('备份文件已删除！');
                location.reload();
            }
        }

        // 清空日志
        function clearLogs() {
            if (confirm('确认清空所有系统日志？')) {
                alert('系统日志已清空！');
                location.reload();
            }
        }

        // 导出日志
        function exportLogs() {
            alert('正在导出系统日志...');
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.settings-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
