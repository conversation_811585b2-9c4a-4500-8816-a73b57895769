# 系统设计说明文档

## 1. 系统概述

### 1.1 系统目标
上海市装饰装修行业协会幕墙信息化平台旨在建立一个功能完善的数字信息库系统，为幕墙行业各类企业和协会管理提供全面的信息化服务。

### 1.2 设计原则
- **可扩展性**: 采用模块化设计，支持功能扩展
- **高可用性**: 通过负载均衡和容错机制保证系统稳定运行
- **安全性**: 多层安全防护，保护数据安全
- **易用性**: 简洁直观的用户界面，降低使用门槛
- **标准化**: 遵循行业标准和规范

### 1.3 技术架构
采用前后端分离的B/S架构，前端使用Vue.js框架，后端使用Spring Boot框架，数据库使用MySQL，缓存使用Redis，搜索引擎使用Elasticsearch。

## 2. 系统架构设计

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户层        │    │   接入层        │    │   前端层        │
│ - 企业用户      │───▶│ - 负载均衡器    │───▶│ - Web前端       │
│ - 协会管理员    │    │ - CDN          │    │ - 管理后台      │
│ - 检查维修企业  │    │ - WAF          │    │ - 移动端        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   监控运维      │    │   外部集成      │    │   网关层        │
│ - 系统监控      │    │ - 政府平台API   │    │ - API网关       │
│ - 日志收集      │    │ - 第三方认证    │    │ - 认证授权      │
│ - 告警系统      │    │ - 短信邮件      │    │ - 限流控制      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据存储层    │    │   数据服务层    │    │   应用服务层    │
│ - 主数据库      │◀───│ - Redis缓存     │◀───│ - 用户管理服务  │
│ - 从数据库      │    │ - Elasticsearch │    │ - 企业管理服务  │
│ - 备份存储      │    │ - 文件存储      │    │ - 项目管理服务  │
│ - 日志数据库    │    │ - 消息队列      │    │ - 协会管理服务  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 服务架构
系统采用微服务架构，主要包括以下服务：

#### 2.2.1 核心业务服务
- **用户管理服务**: 用户注册、登录、权限管理
- **企业管理服务**: 企业信息、资质、人员、业绩管理
- **项目管理服务**: 项目信息、进度、质量管理
- **既有幕墙管理服务**: 幕墙信息、检查、维修管理
- **协会管理服务**: 会员管理、行业监管、标准管理
- **统计分析服务**: 数据统计、报表生成、趋势分析

#### 2.2.2 基础支撑服务
- **认证授权服务**: 统一身份认证和权限控制
- **文件管理服务**: 文件上传、存储、下载管理
- **消息通知服务**: 短信、邮件、站内消息发送
- **日志审计服务**: 操作日志记录和审计
- **系统配置服务**: 系统参数配置和管理

### 2.3 数据架构
#### 2.3.1 数据分层
- **数据接入层**: 数据收集和预处理
- **数据存储层**: 关系型数据库、缓存、搜索引擎
- **数据服务层**: 数据访问接口和服务
- **数据应用层**: 业务逻辑和数据展示

#### 2.3.2 数据流向
```
数据源 → 数据收集 → 数据清洗 → 数据存储 → 数据处理 → 数据展示
   ↓         ↓         ↓         ↓         ↓         ↓
企业录入   格式验证   去重校验   MySQL    业务逻辑   用户界面
外部接口   完整性检查 数据标准化  Redis    统计分析   报表展示
批量导入   安全检查   数据分类   ES       趋势预测   图表可视化
```

## 3. 模块设计

### 3.1 用户管理模块
#### 3.1.1 功能组件
- **用户注册**: 支持多类型用户注册和审核
- **用户认证**: 用户名/密码登录，短信验证
- **权限管理**: 基于角色的权限控制（RBAC）
- **会话管理**: JWT Token会话管理

#### 3.1.2 核心类设计
```java
// 用户实体类
public class User {
    private Long userId;
    private String username;
    private String password;
    private String email;
    private String phone;
    private String realName;
    private UserType userType;
    private UserStatus status;
    private Date createTime;
    private Date updateTime;
}

// 角色实体类
public class Role {
    private Long roleId;
    private String roleName;
    private String roleCode;
    private String description;
    private RoleStatus status;
    private Date createTime;
}

// 权限实体类
public class Permission {
    private Long permissionId;
    private String permissionName;
    private String permissionCode;
    private String resourceType;
    private String resourceUrl;
    private Long parentId;
    private Integer sortOrder;
}
```

### 3.2 企业管理模块
#### 3.2.1 功能组件
- **企业信息管理**: 企业基本信息维护
- **资质管理**: 企业资质证书管理
- **人员管理**: 企业人员信息管理
- **业绩管理**: 企业项目业绩管理

#### 3.2.2 核心类设计
```java
// 企业实体类
public class Company {
    private Long companyId;
    private String companyName;
    private String companyCode;
    private String legalPerson;
    private String businessLicense;
    private CompanyType companyType;
    private String address;
    private String contactPerson;
    private String contactPhone;
    private String contactEmail;
    private String description;
    private CompanyStatus status;
    private Date createTime;
    private Date updateTime;
    private Long createUserId;
}

// 企业资质类
public class CompanyQualification {
    private Long qualificationId;
    private Long companyId;
    private String qualificationName;
    private String qualificationCode;
    private String qualificationLevel;
    private String issuingAuthority;
    private Date issueDate;
    private Date expireDate;
    private String certificateFile;
    private QualificationStatus status;
    private Date createTime;
}
```

### 3.3 项目管理模块
#### 3.3.1 功能组件
- **项目信息管理**: 项目基本信息维护
- **进度管理**: 项目进度跟踪和更新
- **质量管理**: 项目质量检查记录
- **文档管理**: 项目相关文档管理

#### 3.3.2 核心类设计
```java
// 项目实体类
public class Project {
    private Long projectId;
    private String projectName;
    private String projectCode;
    private String projectLocation;
    private String ownerName;
    private String designCompany;
    private String constructionCompany;
    private String supervisionCompany;
    private BigDecimal projectArea;
    private BigDecimal curtainWallArea;
    private String curtainWallType;
    private Date startDate;
    private Date plannedCompletionDate;
    private Date actualCompletionDate;
    private ProjectStatus projectStatus;
    private String description;
    private Date createTime;
    private Date updateTime;
    private Long createUserId;
}

// 项目进度类
public class ProjectProgress {
    private Long progressId;
    private Long projectId;
    private String progressName;
    private String progressDescription;
    private BigDecimal completionRate;
    private Date plannedDate;
    private Date actualDate;
    private ProgressStatus status;
    private String remark;
    private Date createTime;
    private Long createUserId;
}
```

### 3.4 既有幕墙管理模块
#### 3.4.1 功能组件
- **幕墙信息管理**: 既有幕墙基本信息
- **检查管理**: 幕墙检查记录和报告
- **维修管理**: 幕墙维修记录和跟踪
- **预警管理**: 安全隐患预警系统

#### 3.4.2 核心类设计
```java
// 既有幕墙实体类
public class ExistingCurtainWall {
    private Long curtainWallId;
    private String buildingName;
    private String buildingAddress;
    private String ownerName;
    private String propertyCompany;
    private Date constructionDate;
    private String originalConstructor;
    private String curtainWallType;
    private String frameMaterial;
    private String glassType;
    private BigDecimal totalArea;
    private Integer floorCount;
    private String structuralForm;
    private String technicalParameters;
    private SafetyLevel safetyLevel;
    private Date createTime;
    private Date updateTime;
}

// 幕墙检查类
public class CurtainWallInspection {
    private Long inspectionId;
    private Long curtainWallId;
    private Long inspectionCompanyId;
    private String inspectionType;
    private Date inspectionDate;
    private String inspector;
    private String inspectionStandard;
    private String inspectionContent;
    private String safetyAssessment;
    private String problemsFound;
    private String recommendations;
    private String inspectionReport;
    private InspectionStatus status;
    private Date createTime;
}
```

## 4. 接口设计

### 4.1 RESTful API设计规范
#### 4.1.1 URL设计规范
- 使用名词复数形式：`/api/v1/companies`
- 使用HTTP方法表示操作：GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
- 使用路径参数表示资源ID：`/api/v1/companies/{id}`
- 使用查询参数进行过滤和分页：`/api/v1/companies?type=material&page=1&size=10`

#### 4.1.2 响应格式规范
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据
  },
  "timestamp": "2024-12-01T10:00:00Z"
}
```

### 4.2 核心API接口
#### 4.2.1 用户管理API
```
POST   /api/v1/auth/login          # 用户登录
POST   /api/v1/auth/logout         # 用户退出
POST   /api/v1/auth/refresh        # 刷新Token
GET    /api/v1/users               # 获取用户列表
POST   /api/v1/users               # 创建用户
GET    /api/v1/users/{id}          # 获取用户详情
PUT    /api/v1/users/{id}          # 更新用户信息
DELETE /api/v1/users/{id}          # 删除用户
```

#### 4.2.2 企业管理API
```
GET    /api/v1/companies           # 获取企业列表
POST   /api/v1/companies           # 创建企业
GET    /api/v1/companies/{id}      # 获取企业详情
PUT    /api/v1/companies/{id}      # 更新企业信息
DELETE /api/v1/companies/{id}      # 删除企业
GET    /api/v1/companies/{id}/qualifications  # 获取企业资质
POST   /api/v1/companies/{id}/qualifications  # 添加企业资质
```

#### 4.2.3 项目管理API
```
GET    /api/v1/projects            # 获取项目列表
POST   /api/v1/projects            # 创建项目
GET    /api/v1/projects/{id}       # 获取项目详情
PUT    /api/v1/projects/{id}       # 更新项目信息
DELETE /api/v1/projects/{id}       # 删除项目
GET    /api/v1/projects/{id}/progress      # 获取项目进度
POST   /api/v1/projects/{id}/progress      # 添加项目进度
```

## 5. 安全设计

### 5.1 认证授权
- **JWT Token**: 使用JWT Token进行无状态认证
- **角色权限**: 基于RBAC的角色权限控制
- **API权限**: 接口级别的权限控制
- **数据权限**: 数据级别的权限控制

### 5.2 数据安全
- **传输加密**: HTTPS加密传输
- **存储加密**: 敏感数据加密存储
- **SQL注入防护**: 使用参数化查询
- **XSS防护**: 输入输出过滤和转义

### 5.3 系统安全
- **防火墙**: Web应用防火墙（WAF）
- **限流控制**: API访问频率限制
- **异常监控**: 异常访问监控和告警
- **日志审计**: 完整的操作日志记录

## 6. 性能设计

### 6.1 缓存策略
- **Redis缓存**: 热点数据缓存
- **浏览器缓存**: 静态资源缓存
- **CDN缓存**: 内容分发网络缓存
- **数据库缓存**: 查询结果缓存

### 6.2 数据库优化
- **读写分离**: 主从数据库读写分离
- **索引优化**: 合理设计数据库索引
- **分页查询**: 大数据量分页处理
- **连接池**: 数据库连接池管理

### 6.3 系统优化
- **负载均衡**: 多实例负载均衡
- **异步处理**: 耗时操作异步处理
- **资源压缩**: 静态资源压缩
- **懒加载**: 按需加载数据

## 7. 监控运维

### 7.1 系统监控
- **性能监控**: CPU、内存、磁盘、网络监控
- **应用监控**: 应用性能和错误监控
- **业务监控**: 关键业务指标监控
- **日志监控**: 日志收集和分析

### 7.2 告警机制
- **阈值告警**: 基于阈值的自动告警
- **异常告警**: 系统异常自动告警
- **业务告警**: 业务异常告警
- **通知方式**: 短信、邮件、钉钉等多种通知方式

### 7.3 运维管理
- **自动化部署**: CI/CD自动化部署
- **配置管理**: 统一配置管理
- **版本管理**: 应用版本管理
- **备份恢复**: 数据备份和恢复策略
