# API接口设计文档

## 1. API设计规范

### 1.1 RESTful设计原则
- 使用HTTP方法表示操作：GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
- 使用名词复数形式表示资源：`/api/v1/companies`
- 使用路径参数表示资源ID：`/api/v1/companies/{id}`
- 使用查询参数进行过滤和分页：`?page=1&size=10&type=material`

### 1.2 URL命名规范
- 基础路径：`/api/v1`
- 资源路径：使用小写字母和连字符
- 版本控制：在URL中包含版本号
- 嵌套资源：`/api/v1/companies/{id}/qualifications`

### 1.3 HTTP状态码
- 200 OK：请求成功
- 201 Created：创建成功
- 400 Bad Request：请求参数错误
- 401 Unauthorized：未授权
- 403 Forbidden：权限不足
- 404 Not Found：资源不存在
- 500 Internal Server Error：服务器内部错误

### 1.4 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据
  },
  "timestamp": "2024-12-01T10:00:00Z"
}
```

### 1.5 分页格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [],
    "pagination": {
      "page": 1,
      "size": 10,
      "total": 100,
      "pages": 10
    }
  },
  "timestamp": "2024-12-01T10:00:00Z"
}
```

## 2. 认证授权API

### 2.1 用户登录
```
POST /api/v1/auth/login
```

**请求参数：**
```json
{
  "username": "string",
  "password": "string",
  "captcha": "string",
  "captchaKey": "string"
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_string",
    "expiresIn": 7200,
    "userInfo": {
      "userId": 1,
      "username": "admin",
      "realName": "管理员",
      "userType": 5,
      "permissions": ["user:view", "company:manage"]
    }
  }
}
```

### 2.2 用户退出
```
POST /api/v1/auth/logout
```

**请求头：**
```
Authorization: Bearer {token}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "退出成功"
}
```

### 2.3 刷新Token
```
POST /api/v1/auth/refresh
```

**请求参数：**
```json
{
  "refreshToken": "refresh_token_string"
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "刷新成功",
  "data": {
    "token": "new_access_token",
    "expiresIn": 7200
  }
}
```

## 3. 用户管理API

### 3.1 获取用户列表
```
GET /api/v1/users
```

**查询参数：**
- page: 页码（默认1）
- size: 每页大小（默认10）
- userType: 用户类型
- status: 用户状态
- keyword: 搜索关键词

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "userId": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "realName": "管理员",
        "userType": 5,
        "status": 1,
        "createTime": "2024-01-01T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 10,
      "total": 1,
      "pages": 1
    }
  }
}
```

### 3.2 创建用户
```
POST /api/v1/users
```

**请求参数：**
```json
{
  "username": "newuser",
  "password": "password123",
  "email": "<EMAIL>",
  "phone": "13800138001",
  "realName": "新用户",
  "userType": 1
}
```

### 3.3 获取用户详情
```
GET /api/v1/users/{id}
```

### 3.4 更新用户信息
```
PUT /api/v1/users/{id}
```

### 3.5 删除用户
```
DELETE /api/v1/users/{id}
```

## 4. 企业管理API

### 4.1 获取企业列表
```
GET /api/v1/companies
```

**查询参数：**
- page: 页码
- size: 每页大小
- companyType: 企业类型
- status: 企业状态
- keyword: 搜索关键词
- region: 地区

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "companyId": 1,
        "companyName": "上海某幕墙材料有限公司",
        "companyCode": "CW001",
        "legalPerson": "张三",
        "businessLicense": "91310000123456789X",
        "companyType": 1,
        "address": "上海市浦东新区某某路123号",
        "contactPerson": "李四",
        "contactPhone": "021-12345678",
        "contactEmail": "<EMAIL>",
        "status": 1,
        "createTime": "2024-01-01T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 10,
      "total": 1,
      "pages": 1
    }
  }
}
```

### 4.2 创建企业
```
POST /api/v1/companies
```

**请求参数：**
```json
{
  "companyName": "上海某幕墙材料有限公司",
  "legalPerson": "张三",
  "businessLicense": "91310000123456789X",
  "companyType": 1,
  "address": "上海市浦东新区某某路123号",
  "contactPerson": "李四",
  "contactPhone": "021-12345678",
  "contactEmail": "<EMAIL>",
  "description": "专业从事幕墙材料生产和销售"
}
```

### 4.3 获取企业详情
```
GET /api/v1/companies/{id}
```

### 4.4 更新企业信息
```
PUT /api/v1/companies/{id}
```

### 4.5 删除企业
```
DELETE /api/v1/companies/{id}
```

### 4.6 获取企业资质列表
```
GET /api/v1/companies/{id}/qualifications
```

### 4.7 添加企业资质
```
POST /api/v1/companies/{id}/qualifications
```

**请求参数：**
```json
{
  "qualificationName": "建筑幕墙工程专业承包一级",
  "qualificationCode": "ZZ001",
  "qualificationLevel": "一级",
  "issuingAuthority": "住房和城乡建设部",
  "issueDate": "2023-01-01",
  "expireDate": "2028-01-01",
  "certificateFile": "/uploads/certificates/cert001.pdf"
}
```

### 4.8 获取企业人员列表
```
GET /api/v1/companies/{id}/personnel
```

### 4.9 添加企业人员
```
POST /api/v1/companies/{id}/personnel
```

### 4.10 获取企业业绩列表
```
GET /api/v1/companies/{id}/performances
```

### 4.11 添加企业业绩
```
POST /api/v1/companies/{id}/performances
```

## 5. 项目管理API

### 5.1 获取项目列表
```
GET /api/v1/projects
```

**查询参数：**
- page: 页码
- size: 每页大小
- projectStatus: 项目状态
- startDate: 开始日期
- endDate: 结束日期
- keyword: 搜索关键词
- region: 地区

### 5.2 创建项目
```
POST /api/v1/projects
```

**请求参数：**
```json
{
  "projectName": "上海某商业大厦幕墙工程",
  "projectCode": "PJ001",
  "projectLocation": "上海市黄浦区",
  "ownerName": "上海某房地产开发有限公司",
  "designCompany": "上海某建筑设计院",
  "constructionCompany": "上海某建筑工程有限公司",
  "supervisionCompany": "上海某监理公司",
  "projectArea": 50000.00,
  "curtainWallArea": 15000.00,
  "curtainWallType": "玻璃幕墙",
  "startDate": "2024-01-01",
  "plannedCompletionDate": "2024-12-31",
  "description": "高层商业建筑玻璃幕墙工程"
}
```

### 5.3 获取项目详情
```
GET /api/v1/projects/{id}
```

### 5.4 更新项目信息
```
PUT /api/v1/projects/{id}
```

### 5.5 删除项目
```
DELETE /api/v1/projects/{id}
```

### 5.6 获取项目进度列表
```
GET /api/v1/projects/{id}/progress
```

### 5.7 添加项目进度
```
POST /api/v1/projects/{id}/progress
```

**请求参数：**
```json
{
  "progressName": "基础施工",
  "progressDescription": "地基基础施工阶段",
  "completionRate": 30.00,
  "plannedDate": "2024-03-01",
  "actualDate": "2024-03-05",
  "status": 3,
  "remark": "因天气原因延期5天"
}
```

### 5.8 获取项目质量检查列表
```
GET /api/v1/projects/{id}/quality
```

### 5.9 添加质量检查记录
```
POST /api/v1/projects/{id}/quality
```

## 6. 既有幕墙管理API

### 6.1 获取既有幕墙列表
```
GET /api/v1/curtain-walls
```

### 6.2 创建既有幕墙
```
POST /api/v1/curtain-walls
```

**请求参数：**
```json
{
  "buildingName": "上海某写字楼",
  "buildingAddress": "上海市浦东新区某某路456号",
  "ownerName": "上海某物业管理有限公司",
  "propertyCompany": "上海某物业服务公司",
  "constructionDate": "2010-01-01",
  "originalConstructor": "上海某建筑工程有限公司",
  "curtainWallType": "玻璃幕墙",
  "frameMaterial": "铝合金",
  "glassType": "中空玻璃",
  "totalArea": 8000.00,
  "floorCount": 20,
  "structuralForm": "框架结构",
  "technicalParameters": "详细技术参数说明",
  "safetyLevel": 1
}
```

### 6.3 获取幕墙检查记录
```
GET /api/v1/curtain-walls/{id}/inspections
```

### 6.4 添加检查记录
```
POST /api/v1/curtain-walls/{id}/inspections
```

### 6.5 获取幕墙维修记录
```
GET /api/v1/curtain-walls/{id}/maintenances
```

### 6.6 添加维修记录
```
POST /api/v1/curtain-walls/{id}/maintenances
```

## 7. 统计分析API

### 7.1 获取企业统计数据
```
GET /api/v1/statistics/companies
```

**查询参数：**
- dimension: 统计维度（type/region/scale）
- startDate: 开始日期
- endDate: 结束日期

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalCount": 150,
    "typeDistribution": [
      {"type": "幕墙材料企业", "count": 50, "percentage": 33.33},
      {"type": "幕墙施工企业", "count": 60, "percentage": 40.00},
      {"type": "既有幕墙维修企业", "count": 25, "percentage": 16.67},
      {"type": "既有幕墙检查服务企业", "count": 15, "percentage": 10.00}
    ],
    "regionDistribution": [
      {"region": "浦东新区", "count": 45, "percentage": 30.00},
      {"region": "黄浦区", "count": 30, "percentage": 20.00},
      {"region": "静安区", "count": 25, "percentage": 16.67}
    ]
  }
}
```

### 7.2 获取项目统计数据
```
GET /api/v1/statistics/projects
```

### 7.3 获取行业趋势数据
```
GET /api/v1/statistics/trends
```

### 7.4 生成自定义报表
```
POST /api/v1/statistics/reports
```

**请求参数：**
```json
{
  "reportName": "月度企业统计报表",
  "reportType": "company",
  "dimensions": ["type", "region"],
  "dateRange": {
    "startDate": "2024-01-01",
    "endDate": "2024-01-31"
  },
  "filters": {
    "companyType": [1, 2],
    "status": 1
  }
}
```

## 8. 文件管理API

### 8.1 文件上传
```
POST /api/v1/files/upload
```

**请求参数：**
- file: 文件（multipart/form-data）
- type: 文件类型（certificate/document/image）

**响应数据：**
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "fileId": "file123456",
    "fileName": "certificate.pdf",
    "fileSize": 1024000,
    "fileType": "application/pdf",
    "filePath": "/uploads/certificates/2024/01/certificate.pdf",
    "fileUrl": "https://domain.com/uploads/certificates/2024/01/certificate.pdf"
  }
}
```

### 8.2 文件下载
```
GET /api/v1/files/{fileId}/download
```

### 8.3 文件删除
```
DELETE /api/v1/files/{fileId}
```

## 9. 系统管理API

### 9.1 获取系统配置
```
GET /api/v1/system/configs
```

### 9.2 更新系统配置
```
PUT /api/v1/system/configs
```

### 9.3 获取操作日志
```
GET /api/v1/system/logs
```

### 9.4 获取数据字典
```
GET /api/v1/system/dictionaries
```

## 10. 错误处理

### 10.1 错误响应格式
```json
{
  "code": 400,
  "message": "请求参数错误",
  "errors": [
    {
      "field": "username",
      "message": "用户名不能为空"
    }
  ],
  "timestamp": "2024-12-01T10:00:00Z"
}
```

### 10.2 常见错误码
- 1001: 参数验证失败
- 1002: 用户名或密码错误
- 1003: 用户未登录
- 1004: 权限不足
- 1005: 资源不存在
- 2001: 企业名称已存在
- 2002: 项目编号已存在
- 9999: 系统内部错误
