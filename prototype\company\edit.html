<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业编辑 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar {
            background: linear-gradient(45deg, #667eea, #764ba2);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 0;
        }
        .sidebar-menu {
            list-style: none;
            padding: 1rem 0;
            margin: 0;
        }
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        .sidebar-menu a {
            display: block;
            padding: 0.8rem 1.5rem;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background: rgba(102, 126, 234, 0.1);
            border-left-color: #667eea;
            color: #667eea;
        }
        .sidebar-menu i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            padding: 2rem;
        }
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .form-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .form-section {
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #e9ecef;
        }
        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1.5rem;
            padding-left: 1rem;
            border-left: 4px solid #667eea;
        }
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 0.8rem 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 8px;
            padding: 0.8rem 2rem;
            font-weight: 600;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .file-upload-area {
            border: 2px dashed #e9ecef;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }
        .file-upload-area:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }
        .uploaded-file {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.8rem;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 0.5rem;
        }
        .required {
            color: #dc3545;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -250px;
                width: 250px;
                z-index: 1000;
                transition: left 0.3s ease;
            }
            .sidebar.show {
                left: 0;
            }
            .main-content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <button class="navbar-toggler d-lg-none" type="button" onclick="toggleSidebar()">
                <span class="navbar-toggler-icon"></span>
            </button>
            <a class="navbar-brand" href="../dashboard.html">
                <i class="fas fa-building"></i> 幕墙信息化平台
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user"></i> 个人资料</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-cog"></i> 系统设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar" id="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="list.html" class="active"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="../project/list.html"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="../curtain-wall/list.html"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="../statistics/overview.html"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="../system/users.html"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb mb-2">
                                        <li class="breadcrumb-item"><a href="list.html">企业管理</a></li>
                                        <li class="breadcrumb-item active">企业编辑</li>
                                    </ol>
                                </nav>
                                <h2><i class="fas fa-edit"></i> 企业编辑</h2>
                            </div>
                            <div>
                                <button class="btn btn-outline-secondary me-2" onclick="window.location.href='list.html'">
                                    <i class="fas fa-arrow-left"></i> 返回列表
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 企业编辑表单 -->
                    <form id="companyForm">
                        <div class="form-card">
                            <!-- 基本信息 -->
                            <div class="form-section">
                                <h4 class="section-title">基本信息</h4>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">企业名称 <span class="required">*</span></label>
                                        <input type="text" class="form-control" id="companyName" value="上海某幕墙材料有限公司" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">企业类型 <span class="required">*</span></label>
                                        <select class="form-select" id="companyType" required>
                                            <option value="">请选择企业类型</option>
                                            <option value="1" selected>幕墙材料企业</option>
                                            <option value="2">幕墙施工企业</option>
                                            <option value="3">既有幕墙维修企业</option>
                                            <option value="4">既有幕墙检查服务企业</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">统一社会信用代码 <span class="required">*</span></label>
                                        <input type="text" class="form-control" id="businessLicense" value="91310000123456789X" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">法定代表人 <span class="required">*</span></label>
                                        <input type="text" class="form-control" id="legalPerson" value="张三" required>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">注册资本</label>
                                        <input type="text" class="form-control" id="registeredCapital" value="5000万元">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">成立日期</label>
                                        <input type="date" class="form-control" id="establishDate" value="2010-03-15">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">企业地址 <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="address" value="上海市浦东新区某某路123号某某大厦15楼" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">企业描述</label>
                                    <textarea class="form-control" id="description" rows="3">专业从事幕墙材料生产和销售的现代化企业</textarea>
                                </div>
                            </div>

                            <!-- 联系信息 -->
                            <div class="form-section">
                                <h4 class="section-title">联系信息</h4>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">联系人 <span class="required">*</span></label>
                                        <input type="text" class="form-control" id="contactPerson" value="李四" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">联系电话 <span class="required">*</span></label>
                                        <input type="tel" class="form-control" id="contactPhone" value="021-12345678" required>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">邮箱地址</label>
                                        <input type="email" class="form-control" id="contactEmail" value="<EMAIL>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">传真号码</label>
                                        <input type="tel" class="form-control" id="fax" value="021-12345679">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">企业网站</label>
                                    <input type="url" class="form-control" id="website" value="https://www.company.com">
                                </div>
                            </div>

                            <!-- 经营信息 -->
                            <div class="form-section">
                                <h4 class="section-title">经营信息</h4>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">经营范围</label>
                                        <textarea class="form-control" id="businessScope" rows="3">幕墙材料生产、销售；建筑装饰材料销售；技术咨询服务</textarea>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">主营产品</label>
                                        <textarea class="form-control" id="mainProducts" rows="3">玻璃幕墙、铝合金幕墙、石材幕墙、金属幕墙</textarea>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">员工人数</label>
                                        <select class="form-select" id="employeeCount">
                                            <option value="">请选择员工人数</option>
                                            <option value="1-50">1-50人</option>
                                            <option value="51-100" selected>51-100人</option>
                                            <option value="101-200">101-200人</option>
                                            <option value="201-500">201-500人</option>
                                            <option value="500+">500人以上</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">年营业额</label>
                                        <select class="form-select" id="annualRevenue">
                                            <option value="">请选择年营业额</option>
                                            <option value="1000万以下">1000万以下</option>
                                            <option value="1000万-5000万" selected>1000万-5000万</option>
                                            <option value="5000万-1亿">5000万-1亿</option>
                                            <option value="1亿-5亿">1亿-5亿</option>
                                            <option value="5亿以上">5亿以上</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- 文件上传 -->
                            <div class="form-section">
                                <h4 class="section-title">相关文件</h4>
                                <div class="mb-3">
                                    <label class="form-label">营业执照</label>
                                    <div class="file-upload-area" onclick="document.getElementById('businessLicenseFile').click()">
                                        <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                                        <p class="text-muted">点击或拖拽文件到此处上传营业执照</p>
                                        <small class="text-muted">支持 PDF、JPG、PNG 格式，单个文件不超过 10MB</small>
                                    </div>
                                    <input type="file" id="businessLicenseFile" accept=".pdf,.jpg,.jpeg,.png" style="display: none;">
                                    <div class="uploaded-files" id="businessLicenseFiles">
                                        <div class="uploaded-file">
                                            <span><i class="fas fa-file-pdf text-danger"></i> 营业执照.pdf (2.3MB)</span>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="this.parentElement.remove()">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">企业照片</label>
                                    <div class="file-upload-area" onclick="document.getElementById('companyPhotos').click()">
                                        <i class="fas fa-camera fa-2x text-muted mb-2"></i>
                                        <p class="text-muted">点击或拖拽文件到此处上传企业照片</p>
                                        <small class="text-muted">支持 JPG、PNG 格式，可上传多张图片</small>
                                    </div>
                                    <input type="file" id="companyPhotos" accept=".jpg,.jpeg,.png" multiple style="display: none;">
                                    <div class="uploaded-files" id="companyPhotoFiles">
                                        <div class="uploaded-file">
                                            <span><i class="fas fa-file-image text-primary"></i> 企业外观.jpg (1.8MB)</span>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="this.parentElement.remove()">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 提交按钮 -->
                            <div class="text-center">
                                <button type="button" class="btn btn-outline-secondary me-3" onclick="window.location.href='list.html'">
                                    <i class="fas fa-times"></i> 取消
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> 保存企业信息
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 切换侧边栏
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('show');
        }

        // 文件上传处理
        document.getElementById('businessLicenseFile').addEventListener('change', function(e) {
            handleFileUpload(e.target.files, 'businessLicenseFiles');
        });

        document.getElementById('companyPhotos').addEventListener('change', function(e) {
            handleFileUpload(e.target.files, 'companyPhotoFiles');
        });

        function handleFileUpload(files, containerId) {
            const container = document.getElementById(containerId);
            
            Array.from(files).forEach(file => {
                if (file.size > 10 * 1024 * 1024) {
                    alert(`文件 ${file.name} 超过10MB限制！`);
                    return;
                }
                
                const fileDiv = document.createElement('div');
                fileDiv.className = 'uploaded-file';
                
                const icon = file.type.includes('pdf') ? 'fas fa-file-pdf text-danger' : 'fas fa-file-image text-primary';
                
                fileDiv.innerHTML = `
                    <span><i class="${icon}"></i> ${file.name} (${(file.size / 1024 / 1024).toFixed(1)}MB)</span>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                container.appendChild(fileDiv);
            });
        }

        // 表单提交
        document.getElementById('companyForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 表单验证
            const requiredFields = ['companyName', 'companyType', 'businessLicense', 'legalPerson', 'address', 'contactPerson', 'contactPhone'];
            let isValid = true;
            
            requiredFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            if (!isValid) {
                alert('请填写所有必填字段！');
                return;
            }
            
            // 模拟保存
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                alert('企业信息保存成功！');
                window.location.href = 'detail.html?id=1';
            }, 2000);
        });

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const formCard = document.querySelector('.form-card');
            formCard.style.opacity = '0';
            formCard.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                formCard.style.transition = 'all 0.6s ease';
                formCard.style.opacity = '1';
                formCard.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
