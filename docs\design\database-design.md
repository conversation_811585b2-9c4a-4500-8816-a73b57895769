# 数据库设计文档

## 1. 数据库概述

### 1.1 设计原则
- **规范化设计**: 遵循第三范式，减少数据冗余
- **性能优化**: 合理设计索引，优化查询性能
- **扩展性**: 预留扩展字段，支持业务发展
- **完整性**: 使用约束保证数据完整性
- **安全性**: 敏感数据加密存储

### 1.2 命名规范
- **表名**: 使用小写字母和下划线，如 `user_info`
- **字段名**: 使用小写字母和下划线，如 `user_name`
- **索引名**: 使用 `idx_` 前缀，如 `idx_user_email`
- **外键名**: 使用 `fk_` 前缀，如 `fk_user_company`

### 1.3 数据类型规范
- **主键**: 使用 `BIGINT` 类型，自增
- **字符串**: 使用 `VARCHAR`，指定合适长度
- **文本**: 使用 `TEXT` 类型
- **日期时间**: 使用 `DATETIME` 类型
- **金额**: 使用 `DECIMAL(15,2)` 类型
- **状态**: 使用 `TINYINT` 类型

## 2. 表结构设计

### 2.1 用户权限相关表

#### 2.1.1 用户表 (user)
```sql
CREATE TABLE `user` (
  `user_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `password` VARCHAR(255) NOT NULL COMMENT '密码',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
  `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
  `real_name` VARCHAR(50) DEFAULT NULL COMMENT '真实姓名',
  `user_type` TINYINT NOT NULL DEFAULT 1 COMMENT '用户类型:1-幕墙材料企业,2-幕墙施工企业,3-既有幕墙维修企业,4-既有幕墙检查服务企业,5-协会管理员',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用,2-待审核',
  `last_login_time` DATETIME DEFAULT NULL COMMENT '最后登录时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

#### 2.1.2 角色表 (role)
```sql
CREATE TABLE `role` (
  `role_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` VARCHAR(50) NOT NULL COMMENT '角色名称',
  `role_code` VARCHAR(50) NOT NULL COMMENT '角色编码',
  `description` VARCHAR(200) DEFAULT NULL COMMENT '描述',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`role_id`),
  UNIQUE KEY `uk_role_code` (`role_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';
```

#### 2.1.3 权限表 (permission)
```sql
CREATE TABLE `permission` (
  `permission_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `permission_name` VARCHAR(50) NOT NULL COMMENT '权限名称',
  `permission_code` VARCHAR(100) NOT NULL COMMENT '权限编码',
  `resource_type` VARCHAR(20) NOT NULL COMMENT '资源类型:menu-菜单,button-按钮,api-接口',
  `resource_url` VARCHAR(200) DEFAULT NULL COMMENT '资源URL',
  `parent_id` BIGINT DEFAULT 0 COMMENT '父权限ID',
  `sort_order` INT DEFAULT 0 COMMENT '排序',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
  PRIMARY KEY (`permission_id`),
  UNIQUE KEY `uk_permission_code` (`permission_code`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';
```

#### 2.1.4 用户角色关联表 (user_role)
```sql
CREATE TABLE `user_role` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `role_id` BIGINT NOT NULL COMMENT '角色ID',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
  KEY `fk_user_role_user` (`user_id`),
  KEY `fk_user_role_role` (`role_id`),
  CONSTRAINT `fk_user_role_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`),
  CONSTRAINT `fk_user_role_role` FOREIGN KEY (`role_id`) REFERENCES `role` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';
```

#### 2.1.5 角色权限关联表 (role_permission)
```sql
CREATE TABLE `role_permission` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_id` BIGINT NOT NULL COMMENT '角色ID',
  `permission_id` BIGINT NOT NULL COMMENT '权限ID',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
  KEY `fk_role_permission_role` (`role_id`),
  KEY `fk_role_permission_permission` (`permission_id`),
  CONSTRAINT `fk_role_permission_role` FOREIGN KEY (`role_id`) REFERENCES `role` (`role_id`),
  CONSTRAINT `fk_role_permission_permission` FOREIGN KEY (`permission_id`) REFERENCES `permission` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';
```

### 2.2 企业管理相关表

#### 2.2.1 企业表 (company)
```sql
CREATE TABLE `company` (
  `company_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '企业ID',
  `company_name` VARCHAR(200) NOT NULL COMMENT '企业名称',
  `company_code` VARCHAR(50) DEFAULT NULL COMMENT '企业编码',
  `legal_person` VARCHAR(50) DEFAULT NULL COMMENT '法定代表人',
  `business_license` VARCHAR(50) DEFAULT NULL COMMENT '营业执照号',
  `company_type` TINYINT NOT NULL COMMENT '企业类型:1-幕墙材料企业,2-幕墙施工企业,3-既有幕墙维修企业,4-既有幕墙检查服务企业',
  `address` VARCHAR(500) DEFAULT NULL COMMENT '企业地址',
  `contact_person` VARCHAR(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` VARCHAR(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` VARCHAR(100) DEFAULT NULL COMMENT '联系邮箱',
  `description` TEXT DEFAULT NULL COMMENT '企业描述',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用,2-待审核',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user_id` BIGINT NOT NULL COMMENT '创建用户ID',
  PRIMARY KEY (`company_id`),
  UNIQUE KEY `uk_company_name` (`company_name`),
  UNIQUE KEY `uk_business_license` (`business_license`),
  KEY `idx_company_type` (`company_type`),
  KEY `idx_status` (`status`),
  KEY `fk_company_user` (`create_user_id`),
  CONSTRAINT `fk_company_user` FOREIGN KEY (`create_user_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业表';
```

#### 2.2.2 企业资质表 (company_qualification)
```sql
CREATE TABLE `company_qualification` (
  `qualification_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '资质ID',
  `company_id` BIGINT NOT NULL COMMENT '企业ID',
  `qualification_name` VARCHAR(100) NOT NULL COMMENT '资质名称',
  `qualification_code` VARCHAR(50) DEFAULT NULL COMMENT '资质编号',
  `qualification_level` VARCHAR(20) DEFAULT NULL COMMENT '资质等级',
  `issuing_authority` VARCHAR(100) DEFAULT NULL COMMENT '发证机关',
  `issue_date` DATE DEFAULT NULL COMMENT '发证日期',
  `expire_date` DATE DEFAULT NULL COMMENT '到期日期',
  `certificate_file` VARCHAR(500) DEFAULT NULL COMMENT '证书文件',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-无效,1-有效,2-待审核',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`qualification_id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_expire_date` (`expire_date`),
  CONSTRAINT `fk_qualification_company` FOREIGN KEY (`company_id`) REFERENCES `company` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业资质表';
```

#### 2.2.3 企业人员表 (company_personnel)
```sql
CREATE TABLE `company_personnel` (
  `personnel_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '人员ID',
  `company_id` BIGINT NOT NULL COMMENT '企业ID',
  `name` VARCHAR(50) NOT NULL COMMENT '姓名',
  `position` VARCHAR(50) DEFAULT NULL COMMENT '职位',
  `id_card` VARCHAR(18) DEFAULT NULL COMMENT '身份证号',
  `phone` VARCHAR(20) DEFAULT NULL COMMENT '联系电话',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
  `qualification` VARCHAR(200) DEFAULT NULL COMMENT '专业资质',
  `experience` TEXT DEFAULT NULL COMMENT '工作经验',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-离职,1-在职',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`personnel_id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_id_card` (`id_card`),
  CONSTRAINT `fk_personnel_company` FOREIGN KEY (`company_id`) REFERENCES `company` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业人员表';
```

#### 2.2.4 企业业绩表 (company_performance)
```sql
CREATE TABLE `company_performance` (
  `performance_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '业绩ID',
  `company_id` BIGINT NOT NULL COMMENT '企业ID',
  `project_name` VARCHAR(200) NOT NULL COMMENT '项目名称',
  `project_location` VARCHAR(200) DEFAULT NULL COMMENT '项目地点',
  `project_amount` DECIMAL(15,2) DEFAULT NULL COMMENT '项目金额',
  `start_date` DATE DEFAULT NULL COMMENT '开始日期',
  `end_date` DATE DEFAULT NULL COMMENT '结束日期',
  `project_type` VARCHAR(50) DEFAULT NULL COMMENT '项目类型',
  `project_description` TEXT DEFAULT NULL COMMENT '项目描述',
  `certificate_file` VARCHAR(500) DEFAULT NULL COMMENT '证明文件',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`performance_id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_project_amount` (`project_amount`),
  CONSTRAINT `fk_performance_company` FOREIGN KEY (`company_id`) REFERENCES `company` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业业绩表';
```

### 2.3 项目管理相关表

#### 2.3.1 项目表 (project)
```sql
CREATE TABLE `project` (
  `project_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `project_name` VARCHAR(200) NOT NULL COMMENT '项目名称',
  `project_code` VARCHAR(50) DEFAULT NULL COMMENT '项目编号',
  `project_location` VARCHAR(200) DEFAULT NULL COMMENT '项目地点',
  `owner_name` VARCHAR(100) DEFAULT NULL COMMENT '业主单位',
  `design_company` VARCHAR(100) DEFAULT NULL COMMENT '设计单位',
  `construction_company` VARCHAR(100) DEFAULT NULL COMMENT '施工单位',
  `supervision_company` VARCHAR(100) DEFAULT NULL COMMENT '监理单位',
  `project_area` DECIMAL(10,2) DEFAULT NULL COMMENT '项目面积',
  `curtain_wall_area` DECIMAL(10,2) DEFAULT NULL COMMENT '幕墙面积',
  `curtain_wall_type` VARCHAR(50) DEFAULT NULL COMMENT '幕墙类型',
  `start_date` DATE DEFAULT NULL COMMENT '开工日期',
  `planned_completion_date` DATE DEFAULT NULL COMMENT '计划竣工日期',
  `actual_completion_date` DATE DEFAULT NULL COMMENT '实际竣工日期',
  `project_status` TINYINT NOT NULL DEFAULT 1 COMMENT '项目状态:1-规划中,2-施工中,3-已完工,4-已验收',
  `description` TEXT DEFAULT NULL COMMENT '项目描述',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user_id` BIGINT NOT NULL COMMENT '创建用户ID',
  PRIMARY KEY (`project_id`),
  UNIQUE KEY `uk_project_code` (`project_code`),
  KEY `idx_project_status` (`project_status`),
  KEY `idx_start_date` (`start_date`),
  KEY `fk_project_user` (`create_user_id`),
  CONSTRAINT `fk_project_user` FOREIGN KEY (`create_user_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目表';
```

#### 2.3.2 项目进度表 (project_progress)
```sql
CREATE TABLE `project_progress` (
  `progress_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '进度ID',
  `project_id` BIGINT NOT NULL COMMENT '项目ID',
  `progress_name` VARCHAR(100) NOT NULL COMMENT '进度节点',
  `progress_description` TEXT DEFAULT NULL COMMENT '进度描述',
  `completion_rate` DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率',
  `planned_date` DATE DEFAULT NULL COMMENT '计划日期',
  `actual_date` DATE DEFAULT NULL COMMENT '实际日期',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:1-未开始,2-进行中,3-已完成,4-延期',
  `remark` VARCHAR(500) DEFAULT NULL COMMENT '备注',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user_id` BIGINT NOT NULL COMMENT '创建用户ID',
  PRIMARY KEY (`progress_id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_planned_date` (`planned_date`),
  CONSTRAINT `fk_progress_project` FOREIGN KEY (`project_id`) REFERENCES `project` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目进度表';
```

#### 2.3.3 项目质量检查表 (project_quality)
```sql
CREATE TABLE `project_quality` (
  `quality_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '质量检查ID',
  `project_id` BIGINT NOT NULL COMMENT '项目ID',
  `check_item` VARCHAR(100) NOT NULL COMMENT '检查项目',
  `check_standard` VARCHAR(200) DEFAULT NULL COMMENT '检查标准',
  `check_result` VARCHAR(20) NOT NULL COMMENT '检查结果:合格,不合格',
  `check_person` VARCHAR(50) DEFAULT NULL COMMENT '检查人员',
  `check_date` DATE NOT NULL COMMENT '检查日期',
  `problem_description` TEXT DEFAULT NULL COMMENT '问题描述',
  `rectification_measures` TEXT DEFAULT NULL COMMENT '整改措施',
  `rectification_deadline` DATE DEFAULT NULL COMMENT '整改期限',
  `rectification_status` TINYINT DEFAULT 0 COMMENT '整改状态:0-未整改,1-整改中,2-已整改',
  `attachment` VARCHAR(500) DEFAULT NULL COMMENT '附件',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`quality_id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_check_date` (`check_date`),
  CONSTRAINT `fk_quality_project` FOREIGN KEY (`project_id`) REFERENCES `project` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目质量检查表';
```

### 2.4 既有幕墙管理相关表

#### 2.4.1 既有幕墙表 (existing_curtain_wall)
```sql
CREATE TABLE `existing_curtain_wall` (
  `curtain_wall_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '既有幕墙ID',
  `building_name` VARCHAR(200) NOT NULL COMMENT '建筑名称',
  `building_address` VARCHAR(500) NOT NULL COMMENT '建筑地址',
  `owner_name` VARCHAR(100) DEFAULT NULL COMMENT '业主单位',
  `property_company` VARCHAR(100) DEFAULT NULL COMMENT '物业公司',
  `construction_date` DATE DEFAULT NULL COMMENT '建成日期',
  `original_constructor` VARCHAR(100) DEFAULT NULL COMMENT '原施工单位',
  `curtain_wall_type` VARCHAR(50) DEFAULT NULL COMMENT '幕墙类型',
  `frame_material` VARCHAR(50) DEFAULT NULL COMMENT '框架材料',
  `glass_type` VARCHAR(50) DEFAULT NULL COMMENT '玻璃类型',
  `total_area` DECIMAL(10,2) DEFAULT NULL COMMENT '总面积',
  `floor_count` INT DEFAULT NULL COMMENT '楼层数',
  `structural_form` VARCHAR(100) DEFAULT NULL COMMENT '结构形式',
  `technical_parameters` TEXT DEFAULT NULL COMMENT '技术参数',
  `safety_level` TINYINT DEFAULT 1 COMMENT '安全等级:1-安全,2-基本安全,3-不安全',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`curtain_wall_id`),
  KEY `idx_building_name` (`building_name`),
  KEY `idx_construction_date` (`construction_date`),
  KEY `idx_safety_level` (`safety_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='既有幕墙表';
```

#### 2.4.2 幕墙检查表 (curtain_wall_inspection)
```sql
CREATE TABLE `curtain_wall_inspection` (
  `inspection_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '检查ID',
  `curtain_wall_id` BIGINT NOT NULL COMMENT '既有幕墙ID',
  `inspection_company_id` BIGINT NOT NULL COMMENT '检查企业ID',
  `inspection_type` VARCHAR(20) NOT NULL COMMENT '检查类型:定期检查,专项检查,应急检查',
  `inspection_date` DATE NOT NULL COMMENT '检查日期',
  `inspector` VARCHAR(100) DEFAULT NULL COMMENT '检查人员',
  `inspection_standard` VARCHAR(200) DEFAULT NULL COMMENT '检查标准',
  `inspection_content` TEXT DEFAULT NULL COMMENT '检查内容',
  `safety_assessment` VARCHAR(20) DEFAULT NULL COMMENT '安全评估:安全,基本安全,不安全',
  `problems_found` TEXT DEFAULT NULL COMMENT '发现问题',
  `recommendations` TEXT DEFAULT NULL COMMENT '建议措施',
  `inspection_report` VARCHAR(500) DEFAULT NULL COMMENT '检查报告',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:1-已完成,2-待审核,3-已审核',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`inspection_id`),
  KEY `idx_curtain_wall_id` (`curtain_wall_id`),
  KEY `idx_inspection_company_id` (`inspection_company_id`),
  KEY `idx_inspection_date` (`inspection_date`),
  CONSTRAINT `fk_inspection_curtain_wall` FOREIGN KEY (`curtain_wall_id`) REFERENCES `existing_curtain_wall` (`curtain_wall_id`),
  CONSTRAINT `fk_inspection_company` FOREIGN KEY (`inspection_company_id`) REFERENCES `company` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='幕墙检查表';
```

#### 2.4.3 幕墙维修表 (curtain_wall_maintenance)
```sql
CREATE TABLE `curtain_wall_maintenance` (
  `maintenance_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '维修ID',
  `curtain_wall_id` BIGINT NOT NULL COMMENT '既有幕墙ID',
  `maintenance_company_id` BIGINT NOT NULL COMMENT '维修企业ID',
  `maintenance_type` VARCHAR(20) NOT NULL COMMENT '维修类型:日常维护,专项维修,应急维修',
  `problem_description` TEXT NOT NULL COMMENT '问题描述',
  `maintenance_plan` TEXT DEFAULT NULL COMMENT '维修方案',
  `planned_start_date` DATE DEFAULT NULL COMMENT '计划开始日期',
  `planned_end_date` DATE DEFAULT NULL COMMENT '计划结束日期',
  `actual_start_date` DATE DEFAULT NULL COMMENT '实际开始日期',
  `actual_end_date` DATE DEFAULT NULL COMMENT '实际结束日期',
  `maintenance_cost` DECIMAL(15,2) DEFAULT NULL COMMENT '维修费用',
  `materials_used` TEXT DEFAULT NULL COMMENT '使用材料',
  `maintenance_process` TEXT DEFAULT NULL COMMENT '维修过程',
  `quality_acceptance` VARCHAR(20) DEFAULT NULL COMMENT '质量验收:合格,不合格',
  `warranty_period` VARCHAR(20) DEFAULT NULL COMMENT '保修期',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:1-计划中,2-施工中,3-已完成,4-已验收',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`maintenance_id`),
  KEY `idx_curtain_wall_id` (`curtain_wall_id`),
  KEY `idx_maintenance_company_id` (`maintenance_company_id`),
  KEY `idx_planned_start_date` (`planned_start_date`),
  CONSTRAINT `fk_maintenance_curtain_wall` FOREIGN KEY (`curtain_wall_id`) REFERENCES `existing_curtain_wall` (`curtain_wall_id`),
  CONSTRAINT `fk_maintenance_company` FOREIGN KEY (`maintenance_company_id`) REFERENCES `company` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='幕墙维修表';
```

## 3. 索引设计

### 3.1 主键索引
所有表都使用自增的BIGINT类型主键，自动创建主键索引。

### 3.2 唯一索引
- `user` 表：username, email, phone
- `company` 表：company_name, business_license
- `project` 表：project_code
- `role` 表：role_code
- `permission` 表：permission_code

### 3.3 普通索引
- 状态字段：status
- 类型字段：user_type, company_type, project_status
- 日期字段：create_time, update_time, start_date, end_date
- 外键字段：company_id, project_id, user_id

### 3.4 复合索引
```sql
-- 用户角色查询
CREATE INDEX idx_user_role_query ON user_role (user_id, role_id);

-- 角色权限查询
CREATE INDEX idx_role_permission_query ON role_permission (role_id, permission_id);

-- 企业类型状态查询
CREATE INDEX idx_company_type_status ON company (company_type, status);

-- 项目状态日期查询
CREATE INDEX idx_project_status_date ON project (project_status, start_date);

-- 检查日期企业查询
CREATE INDEX idx_inspection_date_company ON curtain_wall_inspection (inspection_date, inspection_company_id);
```

## 4. 数据字典

### 4.1 用户类型 (user_type)
- 1: 幕墙材料企业
- 2: 幕墙施工企业
- 3: 既有幕墙维修企业
- 4: 既有幕墙检查服务企业
- 5: 协会管理员

### 4.2 状态字段 (status)
- 0: 禁用/无效
- 1: 启用/有效
- 2: 待审核

### 4.3 项目状态 (project_status)
- 1: 规划中
- 2: 施工中
- 3: 已完工
- 4: 已验收

### 4.4 安全等级 (safety_level)
- 1: 安全
- 2: 基本安全
- 3: 不安全

## 5. 数据备份策略

### 5.1 备份类型
- **全量备份**: 每周执行一次全量备份
- **增量备份**: 每天执行一次增量备份
- **日志备份**: 每小时备份事务日志

### 5.2 备份存储
- **本地存储**: 保留最近7天的备份文件
- **远程存储**: 备份文件同步到云存储
- **异地备份**: 重要数据异地备份

### 5.3 恢复策略
- **时间点恢复**: 支持任意时间点数据恢复
- **表级恢复**: 支持单表数据恢复
- **灾难恢复**: 制定完整的灾难恢复预案
