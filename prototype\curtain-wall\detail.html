<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>既有幕墙详情 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Microsoft YaHei', sans-serif; }
        .navbar { background: linear-gradient(45deg, #667eea, #764ba2); box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .sidebar { background: white; min-height: calc(100vh - 76px); box-shadow: 2px 0 10px rgba(0,0,0,0.1); padding: 0; }
        .sidebar-menu { list-style: none; padding: 1rem 0; margin: 0; }
        .sidebar-menu li { margin-bottom: 0.5rem; }
        .sidebar-menu a { display: block; padding: 0.8rem 1.5rem; color: #333; text-decoration: none; transition: all 0.3s ease; border-left: 3px solid transparent; }
        .sidebar-menu a:hover, .sidebar-menu a.active { background: rgba(102, 126, 234, 0.1); border-left-color: #667eea; color: #667eea; }
        .sidebar-menu i { width: 20px; margin-right: 10px; }
        .main-content { padding: 2rem; }
        .page-header { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .info-card { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .building-header { display: flex; align-items: center; margin-bottom: 2rem; padding-bottom: 1.5rem; border-bottom: 1px solid #e9ecef; }
        .building-icon { width: 80px; height: 80px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 15px; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin-right: 1.5rem; }
        .safety-level-badge { padding: 0.5rem 1rem; border-radius: 25px; font-size: 0.9rem; font-weight: 600; }
        .level-safe { background: rgba(40, 167, 69, 0.1); color: #28a745; }
        .level-warning { background: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .level-danger { background: rgba(220, 53, 69, 0.1); color: #dc3545; }
        .info-row { display: flex; margin-bottom: 1rem; padding: 0.8rem 0; border-bottom: 1px solid #f8f9fa; }
        .info-label { width: 150px; font-weight: 600; color: #666; flex-shrink: 0; }
        .info-value { flex: 1; color: #333; }
        .nav-tabs .nav-link { border: none; color: #666; font-weight: 500; padding: 1rem 1.5rem; }
        .nav-tabs .nav-link.active { background: none; border-bottom: 3px solid #667eea; color: #667eea; }
        .tab-content { padding: 2rem 0; }
        .inspection-item { background: #f8f9fa; border-radius: 10px; padding: 1.5rem; margin-bottom: 1rem; border-left: 4px solid #667eea; }
        .maintenance-item { background: #f8f9fa; border-radius: 10px; padding: 1.5rem; margin-bottom: 1rem; border-left: 4px solid #28a745; }
        .btn-primary { background: linear-gradient(45deg, #667eea, #764ba2); border: none; border-radius: 8px; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3); }
        .alert-item { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 10px; padding: 1rem; margin-bottom: 1rem; }
        .photo-gallery { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 1rem; }
        .photo-item { border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .photo-item img { width: 100%; height: 150px; object-fit: cover; }
        .photo-caption { padding: 0.5rem; background: white; font-size: 0.9rem; color: #666; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.html"><i class="fas fa-building"></i> 幕墙信息化平台</a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="../company/list.html"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="../project/list.html"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="list.html" class="active"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="../statistics/overview.html"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="../system/users.html"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb mb-2">
                                        <li class="breadcrumb-item"><a href="list.html">既有幕墙</a></li>
                                        <li class="breadcrumb-item active">幕墙详情</li>
                                    </ol>
                                </nav>
                                <h2><i class="fas fa-info-circle"></i> 既有幕墙详情</h2>
                            </div>
                            <div>
                                <button class="btn btn-outline-primary me-2" onclick="window.location.href='inspection.html?id=1'">
                                    <i class="fas fa-search"></i> 检查记录
                                </button>
                                <button class="btn btn-primary" onclick="window.location.href='maintenance.html?id=1'">
                                    <i class="fas fa-tools"></i> 维修记录
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 建筑基本信息 -->
                    <div class="info-card">
                        <div class="building-header">
                            <div class="building-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="building-info">
                                <h2>上海某写字楼</h2>
                                <div class="mb-2">
                                    <span class="safety-level-badge level-safe">安全</span>
                                    <span class="badge bg-info ms-2">玻璃幕墙</span>
                                </div>
                                <p class="text-muted mb-0">建筑编号: CW001 | 建成年份: 2015年</p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-row">
                                    <div class="info-label">建筑名称:</div>
                                    <div class="info-value">上海某写字楼</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">建筑地址:</div>
                                    <div class="info-value">上海市浦东新区某某路456号</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">业主单位:</div>
                                    <div class="info-value">上海某物业管理有限公司</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">建筑高度:</div>
                                    <div class="info-value">120米</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">建筑层数:</div>
                                    <div class="info-value">地上30层，地下3层</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-row">
                                    <div class="info-label">幕墙类型:</div>
                                    <div class="info-value">玻璃幕墙</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">幕墙面积:</div>
                                    <div class="info-value">8,500㎡</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">建成年份:</div>
                                    <div class="info-value">2015年</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">使用年限:</div>
                                    <div class="info-value">9年</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">最近检查:</div>
                                    <div class="info-value">2024年2月15日</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 详细信息标签页 -->
                    <div class="info-card">
                        <ul class="nav nav-tabs" id="buildingTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="inspection-tab" data-bs-toggle="tab" data-bs-target="#inspection" type="button" role="tab">
                                    <i class="fas fa-search"></i> 检查记录
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="maintenance-tab" data-bs-toggle="tab" data-bs-target="#maintenance" type="button" role="tab">
                                    <i class="fas fa-tools"></i> 维修记录
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="alerts-tab" data-bs-toggle="tab" data-bs-target="#alerts" type="button" role="tab">
                                    <i class="fas fa-exclamation-triangle"></i> 安全预警
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="photos-tab" data-bs-toggle="tab" data-bs-target="#photos" type="button" role="tab">
                                    <i class="fas fa-camera"></i> 现场照片
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content" id="buildingTabsContent">
                            <!-- 检查记录 -->
                            <div class="tab-pane fade show active" id="inspection" role="tabpanel">
                                <div class="inspection-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5>定期安全检查</h5>
                                            <p class="mb-1"><strong>检查日期:</strong> 2024年2月15日</p>
                                            <p class="mb-1"><strong>检查人员:</strong> 李工程师（上海某检测公司）</p>
                                            <p class="mb-1"><strong>检查标准:</strong> JGJ102-2003</p>
                                            <p class="mb-0"><strong>检查结果:</strong> 整体状况良好，局部密封胶老化需要关注</p>
                                        </div>
                                        <span class="badge bg-success">合格</span>
                                    </div>
                                </div>
                                <div class="inspection-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5>年度全面检查</h5>
                                            <p class="mb-1"><strong>检查日期:</strong> 2023年12月10日</p>
                                            <p class="mb-1"><strong>检查人员:</strong> 王工程师（上海某检测公司）</p>
                                            <p class="mb-1"><strong>检查标准:</strong> JGJ102-2003</p>
                                            <p class="mb-0"><strong>检查结果:</strong> 发现部分玻璃密封胶开裂，已安排维修</p>
                                        </div>
                                        <span class="badge bg-warning">需整改</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 维修记录 -->
                            <div class="tab-pane fade" id="maintenance" role="tabpanel">
                                <div class="maintenance-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5>密封胶更换</h5>
                                            <p class="mb-1"><strong>维修日期:</strong> 2024年1月20日</p>
                                            <p class="mb-1"><strong>维修单位:</strong> 上海某幕墙维修公司</p>
                                            <p class="mb-1"><strong>维修内容:</strong> 更换老化密封胶约200米</p>
                                            <p class="mb-1"><strong>维修费用:</strong> 15,000元</p>
                                            <p class="mb-0"><strong>质保期:</strong> 2年</p>
                                        </div>
                                        <span class="badge bg-success">已完成</span>
                                    </div>
                                </div>
                                <div class="maintenance-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5>玻璃更换</h5>
                                            <p class="mb-1"><strong>维修日期:</strong> 2023年8月15日</p>
                                            <p class="mb-1"><strong>维修单位:</strong> 上海某幕墙维修公司</p>
                                            <p class="mb-1"><strong>维修内容:</strong> 更换破损玻璃3块</p>
                                            <p class="mb-1"><strong>维修费用:</strong> 8,500元</p>
                                            <p class="mb-0"><strong>质保期:</strong> 5年</p>
                                        </div>
                                        <span class="badge bg-success">已完成</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 安全预警 -->
                            <div class="tab-pane fade" id="alerts" role="tabpanel">
                                <div class="alert-item">
                                    <div class="d-flex align-items-start">
                                        <i class="fas fa-exclamation-triangle text-warning me-3 mt-1"></i>
                                        <div>
                                            <h6>密封胶老化预警</h6>
                                            <p class="mb-1">检测发现部分区域密封胶出现老化现象，建议在6个月内进行更换。</p>
                                            <small class="text-muted">预警时间: 2024年2月15日</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    当前无紧急安全隐患，建议按计划进行定期检查和维护。
                                </div>
                            </div>

                            <!-- 现场照片 -->
                            <div class="tab-pane fade" id="photos" role="tabpanel">
                                <div class="photo-gallery">
                                    <div class="photo-item">
                                        <img src="https://via.placeholder.com/300x200?text=建筑外观" alt="建筑外观">
                                        <div class="photo-caption">建筑外观 - 2024-02-15</div>
                                    </div>
                                    <div class="photo-item">
                                        <img src="https://via.placeholder.com/300x200?text=幕墙细节" alt="幕墙细节">
                                        <div class="photo-caption">幕墙细节 - 2024-02-15</div>
                                    </div>
                                    <div class="photo-item">
                                        <img src="https://via.placeholder.com/300x200?text=密封胶状况" alt="密封胶状况">
                                        <div class="photo-caption">密封胶状况 - 2024-02-15</div>
                                    </div>
                                    <div class="photo-item">
                                        <img src="https://via.placeholder.com/300x200?text=维修前" alt="维修前">
                                        <div class="photo-caption">维修前 - 2024-01-18</div>
                                    </div>
                                    <div class="photo-item">
                                        <img src="https://via.placeholder.com/300x200?text=维修后" alt="维修后">
                                        <div class="photo-caption">维修后 - 2024-01-22</div>
                                    </div>
                                    <div class="photo-item">
                                        <img src="https://via.placeholder.com/300x200?text=整体效果" alt="整体效果">
                                        <div class="photo-caption">整体效果 - 2024-02-15</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.info-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
