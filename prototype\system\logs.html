<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作日志 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Microsoft YaHei', sans-serif; }
        .navbar { background: linear-gradient(45deg, #667eea, #764ba2); box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .sidebar { background: white; min-height: calc(100vh - 76px); box-shadow: 2px 0 10px rgba(0,0,0,0.1); padding: 0; }
        .sidebar-menu { list-style: none; padding: 1rem 0; margin: 0; }
        .sidebar-menu li { margin-bottom: 0.5rem; }
        .sidebar-menu a { display: block; padding: 0.8rem 1.5rem; color: #333; text-decoration: none; transition: all 0.3s ease; border-left: 3px solid transparent; }
        .sidebar-menu a:hover, .sidebar-menu a.active { background: rgba(102, 126, 234, 0.1); border-left-color: #667eea; color: #667eea; }
        .sidebar-menu i { width: 20px; margin-right: 10px; }
        .main-content { padding: 2rem; }
        .page-header { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .search-filters { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .logs-table { background: white; border-radius: 15px; padding: 1.5rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .btn-primary { background: linear-gradient(45deg, #667eea, #764ba2); border: none; border-radius: 8px; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3); }
        .log-level-badge { padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.8rem; font-weight: 500; }
        .level-info { background: rgba(23, 162, 184, 0.1); color: #17a2b8; }
        .level-warning { background: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .level-error { background: rgba(220, 53, 69, 0.1); color: #dc3545; }
        .level-debug { background: rgba(108, 117, 125, 0.1); color: #6c757d; }
        .table th { border-top: none; font-weight: 600; color: #333; background: #f8f9fa; }
        .log-detail { background: #f8f9fa; border-radius: 8px; padding: 1rem; margin-top: 0.5rem; font-family: 'Courier New', monospace; font-size: 0.9rem; }
        .stats-cards { margin-bottom: 2rem; }
        .stats-card { background: white; border-radius: 15px; padding: 1.5rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); text-align: center; transition: transform 0.3s ease; }
        .stats-card:hover { transform: translateY(-5px); }
        .stats-number { font-size: 2rem; font-weight: 700; margin-bottom: 0.5rem; }
        .stats-label { color: #666; font-size: 0.9rem; }
        .log-item { padding: 0.8rem; background: #f8f9fa; border-radius: 8px; margin-bottom: 0.5rem; font-family: 'Courier New', monospace; font-size: 0.9rem; }
        .log-item.log-info { border-left: 4px solid #17a2b8; }
        .log-item.log-warning { border-left: 4px solid #ffc107; }
        .log-item.log-error { border-left: 4px solid #dc3545; }
        .log-item.log-debug { border-left: 4px solid #6c757d; }
        .real-time-logs { max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.html"><i class="fas fa-building"></i> 幕墙信息化平台</a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="../company/list.html"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="../project/list.html"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="../curtain-wall/list.html"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="../statistics/overview.html"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="users.html" class="active"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb mb-2">
                                        <li class="breadcrumb-item"><a href="users.html">系统管理</a></li>
                                        <li class="breadcrumb-item active">操作日志</li>
                                    </ol>
                                </nav>
                                <h2><i class="fas fa-history"></i> 操作日志</h2>
                                <p class="text-muted mb-0">系统操作记录和安全审计</p>
                            </div>
                            <div>
                                <button class="btn btn-outline-primary me-2" onclick="window.location.href='users.html'">
                                    <i class="fas fa-users"></i> 用户管理
                                </button>
                                <button class="btn btn-primary" onclick="exportLogs()">
                                    <i class="fas fa-download"></i> 导出日志
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 统计卡片 -->
                    <div class="row stats-cards">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-info">1,247</div>
                                <div class="stats-label">今日操作</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-warning">23</div>
                                <div class="stats-label">警告日志</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-danger">5</div>
                                <div class="stats-label">错误日志</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-success">45</div>
                                <div class="stats-label">活跃用户</div>
                            </div>
                        </div>
                    </div>

                    <!-- 搜索筛选 -->
                    <div class="search-filters">
                        <form class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">日志级别</label>
                                <select class="form-select">
                                    <option value="">全部级别</option>
                                    <option value="info">信息</option>
                                    <option value="warning">警告</option>
                                    <option value="error">错误</option>
                                    <option value="debug">调试</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">操作用户</label>
                                <input type="text" class="form-control" placeholder="请输入用户名">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">操作模块</label>
                                <select class="form-select">
                                    <option value="">全部模块</option>
                                    <option value="user">用户管理</option>
                                    <option value="company">企业管理</option>
                                    <option value="project">项目管理</option>
                                    <option value="curtain-wall">既有幕墙</option>
                                    <option value="system">系统管理</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    <button type="reset" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo"></i> 重置
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="row">
                        <!-- 操作日志列表 -->
                        <div class="col-lg-8">
                            <div class="logs-table">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5><i class="fas fa-list"></i> 操作日志列表</h5>
                                    <div class="text-muted">
                                        共 <span class="text-primary fw-bold">1,247</span> 条记录
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>时间</th>
                                                <th>级别</th>
                                                <th>用户</th>
                                                <th>操作</th>
                                                <th>IP地址</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>2024-02-20 10:30:15</td>
                                                <td><span class="log-level-badge level-info">信息</span></td>
                                                <td>admin</td>
                                                <td>用户登录</td>
                                                <td>*************</td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="viewLogDetail(1)">
                                                        <i class="fas fa-eye"></i> 详情
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td colspan="6">
                                                    <div class="log-detail" style="display: none;" id="detail-1">
                                                        <strong>详细信息:</strong><br>
                                                        用户 admin 从 IP ************* 成功登录系统<br>
                                                        <strong>浏览器:</strong> Chrome *********<br>
                                                        <strong>操作系统:</strong> Windows 10
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>2024-02-20 10:25:32</td>
                                                <td><span class="log-level-badge level-info">信息</span></td>
                                                <td>company001</td>
                                                <td>更新企业信息</td>
                                                <td>*************</td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="viewLogDetail(2)">
                                                        <i class="fas fa-eye"></i> 详情
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>2024-02-20 10:20:18</td>
                                                <td><span class="log-level-badge level-warning">警告</span></td>
                                                <td>company002</td>
                                                <td>登录失败</td>
                                                <td>192.168.1.108</td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="viewLogDetail(3)">
                                                        <i class="fas fa-eye"></i> 详情
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>2024-02-20 10:15:45</td>
                                                <td><span class="log-level-badge level-info">信息</span></td>
                                                <td>system</td>
                                                <td>系统备份</td>
                                                <td>127.0.0.1</td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="viewLogDetail(4)">
                                                        <i class="fas fa-eye"></i> 详情
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>2024-02-20 10:10:22</td>
                                                <td><span class="log-level-badge level-error">错误</span></td>
                                                <td>inspector001</td>
                                                <td>文件上传失败</td>
                                                <td>192.168.1.112</td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="viewLogDetail(5)">
                                                        <i class="fas fa-eye"></i> 详情
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 分页 -->
                                <nav aria-label="日志列表分页">
                                    <ul class="pagination justify-content-center">
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#" tabindex="-1">上一页</a>
                                        </li>
                                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                                        <li class="page-item">
                                            <a class="page-link" href="#">下一页</a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        </div>

                        <!-- 实时日志 -->
                        <div class="col-lg-4">
                            <div class="logs-table">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5><i class="fas fa-broadcast-tower"></i> 实时日志</h5>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="toggleRealTime()">
                                        <i class="fas fa-pause" id="realTimeIcon"></i> 暂停
                                    </button>
                                </div>
                                
                                <div class="real-time-logs" id="realTimeLogs">
                                    <div class="log-item log-info">
                                        <strong>[INFO]</strong> 2024-02-20 10:35:42<br>
                                        用户 admin 查看企业列表
                                    </div>
                                    <div class="log-item log-info">
                                        <strong>[INFO]</strong> 2024-02-20 10:35:15<br>
                                        用户 company003 更新项目进度
                                    </div>
                                    <div class="log-item log-warning">
                                        <strong>[WARN]</strong> 2024-02-20 10:34:58<br>
                                        检测到异常登录尝试
                                    </div>
                                    <div class="log-item log-info">
                                        <strong>[INFO]</strong> 2024-02-20 10:34:32<br>
                                        系统定时任务执行完成
                                    </div>
                                    <div class="log-item log-info">
                                        <strong>[INFO]</strong> 2024-02-20 10:34:18<br>
                                        用户 inspector002 上传检查报告
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let realTimeEnabled = true;

        // 查看日志详情
        function viewLogDetail(id) {
            const detail = document.getElementById(`detail-${id}`);
            if (detail) {
                detail.style.display = detail.style.display === 'none' ? 'block' : 'none';
            }
        }

        // 导出日志
        function exportLogs() {
            alert('正在导出日志文件...');
        }

        // 切换实时日志
        function toggleRealTime() {
            realTimeEnabled = !realTimeEnabled;
            const icon = document.getElementById('realTimeIcon');
            const button = event.currentTarget;
            
            if (realTimeEnabled) {
                icon.className = 'fas fa-pause';
                button.innerHTML = '<i class="fas fa-pause"></i> 暂停';
                startRealTimeUpdate();
            } else {
                icon.className = 'fas fa-play';
                button.innerHTML = '<i class="fas fa-play"></i> 开始';
                stopRealTimeUpdate();
            }
        }

        // 开始实时更新
        function startRealTimeUpdate() {
            if (window.realTimeInterval) {
                clearInterval(window.realTimeInterval);
            }
            
            window.realTimeInterval = setInterval(() => {
                if (realTimeEnabled) {
                    addNewLog();
                }
            }, 5000);
        }

        // 停止实时更新
        function stopRealTimeUpdate() {
            if (window.realTimeInterval) {
                clearInterval(window.realTimeInterval);
            }
        }

        // 添加新日志
        function addNewLog() {
            const logs = [
                { level: 'info', message: '用户登录成功' },
                { level: 'info', message: '数据更新完成' },
                { level: 'warning', message: '系统资源使用率较高' },
                { level: 'info', message: '定时任务执行' },
                { level: 'error', message: '网络连接异常' }
            ];
            
            const randomLog = logs[Math.floor(Math.random() * logs.length)];
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN', { 
                year: 'numeric', 
                month: '2-digit', 
                day: '2-digit', 
                hour: '2-digit', 
                minute: '2-digit', 
                second: '2-digit' 
            });
            
            const logElement = document.createElement('div');
            logElement.className = `log-item log-${randomLog.level}`;
            logElement.innerHTML = `
                <strong>[${randomLog.level.toUpperCase()}]</strong> ${timeStr}<br>
                ${randomLog.message}
            `;
            
            const realTimeLogs = document.getElementById('realTimeLogs');
            realTimeLogs.insertBefore(logElement, realTimeLogs.firstChild);
            
            // 保持最多显示10条日志
            const logItems = realTimeLogs.querySelectorAll('.log-item');
            if (logItems.length > 10) {
                realTimeLogs.removeChild(logItems[logItems.length - 1]);
            }
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stats-card, .search-filters, .logs-table');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
            
            // 启动实时日志更新
            startRealTimeUpdate();
        });

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            stopRealTimeUpdate();
        });
    </script>
</body>
</html>
