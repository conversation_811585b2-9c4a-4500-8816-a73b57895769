<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>既有幕墙列表 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Microsoft YaHei', sans-serif; }
        .navbar { background: linear-gradient(45deg, #667eea, #764ba2); box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .sidebar { background: white; min-height: calc(100vh - 76px); box-shadow: 2px 0 10px rgba(0,0,0,0.1); padding: 0; }
        .sidebar-menu { list-style: none; padding: 1rem 0; margin: 0; }
        .sidebar-menu li { margin-bottom: 0.5rem; }
        .sidebar-menu a { display: block; padding: 0.8rem 1.5rem; color: #333; text-decoration: none; transition: all 0.3s ease; border-left: 3px solid transparent; }
        .sidebar-menu a:hover, .sidebar-menu a.active { background: rgba(102, 126, 234, 0.1); border-left-color: #667eea; color: #667eea; }
        .sidebar-menu i { width: 20px; margin-right: 10px; }
        .main-content { padding: 2rem; }
        .page-header { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .search-filters { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .curtain-wall-table { background: white; border-radius: 15px; padding: 1.5rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .btn-primary { background: linear-gradient(45deg, #667eea, #764ba2); border: none; border-radius: 8px; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3); }
        .safety-level-badge { padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.8rem; font-weight: 500; }
        .level-safe { background: rgba(40, 167, 69, 0.1); color: #28a745; }
        .level-warning { background: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .level-danger { background: rgba(220, 53, 69, 0.1); color: #dc3545; }
        .table th { border-top: none; font-weight: 600; color: #333; background: #f8f9fa; }
        .action-buttons .btn { margin-right: 0.5rem; margin-bottom: 0.5rem; }
        .stats-cards { margin-bottom: 2rem; }
        .stats-card { background: white; border-radius: 15px; padding: 1.5rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); text-align: center; transition: transform 0.3s ease; }
        .stats-card:hover { transform: translateY(-5px); }
        .stats-number { font-size: 2rem; font-weight: 700; margin-bottom: 0.5rem; }
        .stats-label { color: #666; font-size: 0.9rem; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.html"><i class="fas fa-building"></i> 幕墙信息化平台</a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="../company/list.html"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="../project/list.html"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="list.html" class="active"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="../statistics/overview.html"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="../system/users.html"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2><i class="fas fa-building-columns"></i> 既有幕墙管理</h2>
                                <p class="text-muted mb-0">管理既有建筑幕墙信息和安全状况</p>
                            </div>
                            <div>
                                <button class="btn btn-primary" onclick="window.location.href='detail.html'">
                                    <i class="fas fa-plus"></i> 新增幕墙
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 统计卡片 -->
                    <div class="row stats-cards">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-primary">234</div>
                                <div class="stats-label">幕墙建筑总数</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-success">198</div>
                                <div class="stats-label">安全等级良好</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-warning">24</div>
                                <div class="stats-label">需要关注</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-danger">12</div>
                                <div class="stats-label">存在隐患</div>
                            </div>
                        </div>
                    </div>

                    <!-- 搜索筛选 -->
                    <div class="search-filters">
                        <form class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">建筑名称</label>
                                <input type="text" class="form-control" placeholder="请输入建筑名称">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">安全等级</label>
                                <select class="form-select">
                                    <option value="">全部等级</option>
                                    <option value="1">安全</option>
                                    <option value="2">关注</option>
                                    <option value="3">隐患</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">幕墙类型</label>
                                <select class="form-select">
                                    <option value="">全部类型</option>
                                    <option value="玻璃幕墙">玻璃幕墙</option>
                                    <option value="石材幕墙">石材幕墙</option>
                                    <option value="金属幕墙">金属幕墙</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">建成年代</label>
                                <select class="form-select">
                                    <option value="">全部年代</option>
                                    <option value="2020-">2020年以后</option>
                                    <option value="2010-2020">2010-2020年</option>
                                    <option value="2000-2010">2000-2010年</option>
                                    <option value="-2000">2000年以前</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    <button type="reset" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo"></i> 重置
                                    </button>
                                    <button type="button" class="btn btn-outline-success">
                                        <i class="fas fa-download"></i> 导出
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 幕墙列表 -->
                    <div class="curtain-wall-table">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>建筑名称</th>
                                        <th>建筑地址</th>
                                        <th>业主单位</th>
                                        <th>幕墙类型</th>
                                        <th>建成年份</th>
                                        <th>安全等级</th>
                                        <th>最近检查</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-building fa-2x text-primary"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">上海某写字楼</div>
                                                    <small class="text-muted">CW001</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>上海市浦东新区某某路456号</td>
                                        <td>上海某物业管理有限公司</td>
                                        <td>玻璃幕墙</td>
                                        <td>2015年</td>
                                        <td><span class="safety-level-badge level-safe">安全</span></td>
                                        <td>2024-02-15</td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewCurtainWall(1)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-info" onclick="viewInspection(1)">
                                                    <i class="fas fa-search"></i> 检查
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" onclick="viewMaintenance(1)">
                                                    <i class="fas fa-tools"></i> 维修
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-building fa-2x text-warning"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">上海某商场</div>
                                                    <small class="text-muted">CW002</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>上海市黄浦区某某路789号</td>
                                        <td>上海某商业管理公司</td>
                                        <td>石材幕墙</td>
                                        <td>2008年</td>
                                        <td><span class="safety-level-badge level-warning">关注</span></td>
                                        <td>2024-01-20</td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewCurtainWall(2)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-info" onclick="viewInspection(2)">
                                                    <i class="fas fa-search"></i> 检查
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" onclick="viewMaintenance(2)">
                                                    <i class="fas fa-tools"></i> 维修
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-building fa-2x text-danger"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">上海某酒店</div>
                                                    <small class="text-muted">CW003</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>上海市静安区某某路321号</td>
                                        <td>上海某酒店管理集团</td>
                                        <td>金属幕墙</td>
                                        <td>2005年</td>
                                        <td><span class="safety-level-badge level-danger">隐患</span></td>
                                        <td>2024-02-01</td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewCurtainWall(3)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="urgentInspection(3)">
                                                    <i class="fas fa-exclamation-triangle"></i> 紧急检查
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" onclick="viewMaintenance(3)">
                                                    <i class="fas fa-tools"></i> 维修
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <nav aria-label="幕墙列表分页">
                            <ul class="pagination justify-content-center">
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1">上一页</a>
                                </li>
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                <li class="page-item"><a class="page-link" href="#">3</a></li>
                                <li class="page-item">
                                    <a class="page-link" href="#">下一页</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 查看幕墙详情
        function viewCurtainWall(id) {
            window.location.href = `detail.html?id=${id}`;
        }

        // 查看检查记录
        function viewInspection(id) {
            window.location.href = `inspection.html?id=${id}`;
        }

        // 查看维修记录
        function viewMaintenance(id) {
            window.location.href = `maintenance.html?id=${id}`;
        }

        // 紧急检查
        function urgentInspection(id) {
            if (confirm('确认安排紧急检查？')) {
                alert('紧急检查已安排，将在24小时内完成！');
            }
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stats-card, .search-filters, .curtain-wall-table');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
