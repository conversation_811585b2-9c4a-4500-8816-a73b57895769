<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报表生成 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Microsoft YaHei', sans-serif; }
        .navbar { background: linear-gradient(45deg, #667eea, #764ba2); box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .sidebar { background: white; min-height: calc(100vh - 76px); box-shadow: 2px 0 10px rgba(0,0,0,0.1); padding: 0; }
        .sidebar-menu { list-style: none; padding: 1rem 0; margin: 0; }
        .sidebar-menu li { margin-bottom: 0.5rem; }
        .sidebar-menu a { display: block; padding: 0.8rem 1.5rem; color: #333; text-decoration: none; transition: all 0.3s ease; border-left: 3px solid transparent; }
        .sidebar-menu a:hover, .sidebar-menu a.active { background: rgba(102, 126, 234, 0.1); border-left-color: #667eea; color: #667eea; }
        .sidebar-menu i { width: 20px; margin-right: 10px; }
        .main-content { padding: 2rem; }
        .page-header { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .report-card { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .report-template { background: #f8f9fa; border-radius: 10px; padding: 1.5rem; margin-bottom: 1rem; border-left: 4px solid #667eea; transition: all 0.3s ease; cursor: pointer; }
        .report-template:hover { background: #e9ecef; transform: translateX(5px); }
        .report-template.selected { background: rgba(102, 126, 234, 0.1); border-left-color: #28a745; }
        .report-icon { width: 60px; height: 60px; border-radius: 10px; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; color: white; margin-right: 1rem; }
        .btn-primary { background: linear-gradient(45deg, #667eea, #764ba2); border: none; border-radius: 8px; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3); }
        .form-control, .form-select { border-radius: 8px; border: 2px solid #e9ecef; transition: all 0.3s ease; }
        .form-control:focus, .form-select:focus { border-color: #667eea; box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25); }
        .report-history-item { background: #f8f9fa; border-radius: 10px; padding: 1rem; margin-bottom: 1rem; display: flex; justify-content: space-between; align-items: center; }
        .report-status-badge { padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.8rem; font-weight: 500; }
        .status-completed { background: rgba(40, 167, 69, 0.1); color: #28a745; }
        .status-processing { background: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .status-failed { background: rgba(220, 53, 69, 0.1); color: #dc3545; }
        .preview-area { background: #f8f9fa; border-radius: 10px; padding: 2rem; min-height: 400px; border: 2px dashed #dee2e6; display: flex; align-items: center; justify-content: center; color: #6c757d; }
        .step-indicator { display: flex; justify-content: space-between; margin-bottom: 2rem; }
        .step { flex: 1; text-align: center; position: relative; }
        .step::after { content: ''; position: absolute; top: 20px; left: 50%; width: 100%; height: 2px; background: #dee2e6; z-index: 1; }
        .step:last-child::after { display: none; }
        .step.active::after { background: #667eea; }
        .step.completed::after { background: #28a745; }
        .step-circle { width: 40px; height: 40px; border-radius: 50%; background: #dee2e6; color: #6c757d; display: flex; align-items: center; justify-content: center; margin: 0 auto 0.5rem; position: relative; z-index: 2; }
        .step.active .step-circle { background: #667eea; color: white; }
        .step.completed .step-circle { background: #28a745; color: white; }
        .step-label { font-size: 0.9rem; color: #6c757d; }
        .step.active .step-label { color: #667eea; font-weight: 600; }
        .step.completed .step-label { color: #28a745; font-weight: 600; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.html"><i class="fas fa-building"></i> 幕墙信息化平台</a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="../company/list.html"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="../project/list.html"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="../curtain-wall/list.html"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="overview.html" class="active"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="../system/users.html"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb mb-2">
                                        <li class="breadcrumb-item"><a href="overview.html">统计分析</a></li>
                                        <li class="breadcrumb-item active">报表生成</li>
                                    </ol>
                                </nav>
                                <h2><i class="fas fa-file-alt"></i> 报表生成</h2>
                                <p class="text-muted mb-0">自定义报表生成和管理</p>
                            </div>
                            <div>
                                <button class="btn btn-outline-primary me-2" onclick="window.location.href='overview.html'">
                                    <i class="fas fa-chart-bar"></i> 统计概览
                                </button>
                                <button class="btn btn-primary" onclick="createCustomReport()">
                                    <i class="fas fa-plus"></i> 自定义报表
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 报表生成步骤 -->
                    <div class="report-card">
                        <h5><i class="fas fa-list-ol"></i> 报表生成步骤</h5>
                        <div class="step-indicator">
                            <div class="step active">
                                <div class="step-circle">1</div>
                                <div class="step-label">选择模板</div>
                            </div>
                            <div class="step">
                                <div class="step-circle">2</div>
                                <div class="step-label">配置参数</div>
                            </div>
                            <div class="step">
                                <div class="step-circle">3</div>
                                <div class="step-label">预览报表</div>
                            </div>
                            <div class="step">
                                <div class="step-circle">4</div>
                                <div class="step-label">生成下载</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- 报表模板选择 -->
                        <div class="col-lg-6">
                            <div class="report-card">
                                <h5><i class="fas fa-file-alt"></i> 报表模板</h5>
                                <p class="text-muted">选择预定义的报表模板</p>
                                
                                <div class="report-template" onclick="selectTemplate(1)">
                                    <div class="d-flex align-items-center">
                                        <div class="report-icon" style="background: linear-gradient(45deg, #667eea, #764ba2);">
                                            <i class="fas fa-industry"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-1">企业统计报表</h6>
                                            <small class="text-muted">企业注册、分布、规模等统计信息</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="report-template" onclick="selectTemplate(2)">
                                    <div class="d-flex align-items-center">
                                        <div class="report-icon" style="background: linear-gradient(45deg, #28a745, #20c997);">
                                            <i class="fas fa-project-diagram"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-1">项目统计报表</h6>
                                            <small class="text-muted">项目进度、投资规模、完成情况统计</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="report-template" onclick="selectTemplate(3)">
                                    <div class="d-flex align-items-center">
                                        <div class="report-icon" style="background: linear-gradient(45deg, #ffc107, #fd7e14);">
                                            <i class="fas fa-building-columns"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-1">既有幕墙报表</h6>
                                            <small class="text-muted">既有幕墙安全状况、检查维修统计</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="report-template" onclick="selectTemplate(4)">
                                    <div class="d-flex align-items-center">
                                        <div class="report-icon" style="background: linear-gradient(45deg, #dc3545, #e83e8c);">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-1">综合分析报表</h6>
                                            <small class="text-muted">行业整体发展趋势和综合分析</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 报表配置 -->
                        <div class="col-lg-6">
                            <div class="report-card">
                                <h5><i class="fas fa-cog"></i> 报表配置</h5>
                                <p class="text-muted">设置报表参数和筛选条件</p>
                                
                                <form id="reportConfigForm">
                                    <div class="mb-3">
                                        <label class="form-label">报表名称</label>
                                        <input type="text" class="form-control" placeholder="请输入报表名称">
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">统计周期</label>
                                            <select class="form-select">
                                                <option value="">请选择统计周期</option>
                                                <option value="month">本月</option>
                                                <option value="quarter">本季度</option>
                                                <option value="year">本年度</option>
                                                <option value="custom">自定义</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">报表格式</label>
                                            <select class="form-select">
                                                <option value="pdf">PDF格式</option>
                                                <option value="excel">Excel格式</option>
                                                <option value="word">Word格式</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">开始日期</label>
                                            <input type="date" class="form-control">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">结束日期</label>
                                            <input type="date" class="form-control">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">筛选条件</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="includeCharts">
                                            <label class="form-check-label" for="includeCharts">包含图表</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="includeDetails">
                                            <label class="form-check-label" for="includeDetails">包含详细数据</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="includeAnalysis">
                                            <label class="form-check-label" for="includeAnalysis">包含分析说明</label>
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-outline-primary" onclick="previewReport()">
                                            <i class="fas fa-eye"></i> 预览报表
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="generateReport()">
                                            <i class="fas fa-download"></i> 生成报表
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- 报表预览 -->
                    <div class="report-card">
                        <h5><i class="fas fa-eye"></i> 报表预览</h5>
                        <div class="preview-area" id="previewArea">
                            <div class="text-center">
                                <i class="fas fa-file-alt fa-3x mb-3"></i>
                                <p>请选择报表模板并配置参数后预览</p>
                            </div>
                        </div>
                    </div>

                    <!-- 报表历史 -->
                    <div class="report-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5><i class="fas fa-history"></i> 报表历史</h5>
                            <button class="btn btn-outline-secondary btn-sm" onclick="clearHistory()">
                                <i class="fas fa-trash"></i> 清空历史
                            </button>
                        </div>
                        
                        <div class="report-history-item">
                            <div>
                                <h6 class="mb-1">2024年2月企业统计报表</h6>
                                <small class="text-muted">生成时间: 2024-02-20 14:30:25 | 格式: PDF</small>
                            </div>
                            <div>
                                <span class="report-status-badge status-completed me-2">已完成</span>
                                <button class="btn btn-sm btn-outline-primary" onclick="downloadReport(1)">
                                    <i class="fas fa-download"></i> 下载
                                </button>
                            </div>
                        </div>
                        
                        <div class="report-history-item">
                            <div>
                                <h6 class="mb-1">2024年1月项目统计报表</h6>
                                <small class="text-muted">生成时间: 2024-02-15 09:15:42 | 格式: Excel</small>
                            </div>
                            <div>
                                <span class="report-status-badge status-completed me-2">已完成</span>
                                <button class="btn btn-sm btn-outline-primary" onclick="downloadReport(2)">
                                    <i class="fas fa-download"></i> 下载
                                </button>
                            </div>
                        </div>
                        
                        <div class="report-history-item">
                            <div>
                                <h6 class="mb-1">既有幕墙安全状况报表</h6>
                                <small class="text-muted">生成时间: 2024-02-18 16:45:18 | 格式: PDF</small>
                            </div>
                            <div>
                                <span class="report-status-badge status-processing me-2">生成中</span>
                                <button class="btn btn-sm btn-outline-secondary" disabled>
                                    <i class="fas fa-spinner fa-spin"></i> 处理中
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedTemplate = null;

        // 选择报表模板
        function selectTemplate(templateId) {
            // 移除之前的选中状态
            document.querySelectorAll('.report-template').forEach(template => {
                template.classList.remove('selected');
            });
            
            // 添加选中状态
            event.currentTarget.classList.add('selected');
            selectedTemplate = templateId;
            
            // 更新步骤状态
            updateStepStatus(2);
        }

        // 预览报表
        function previewReport() {
            if (!selectedTemplate) {
                alert('请先选择报表模板');
                return;
            }
            
            const previewArea = document.getElementById('previewArea');
            previewArea.innerHTML = `
                <div class="w-100">
                    <div class="text-center mb-3">
                        <h4>报表预览</h4>
                        <p class="text-muted">这是报表的预览效果</p>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6>统计数据</h6>
                                    <p>企业总数: 156家</p>
                                    <p>项目总数: 89个</p>
                                    <p>既有幕墙: 234栋</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6>图表展示</h6>
                                    <div style="height: 100px; background: #f8f9fa; border-radius: 5px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-chart-bar fa-2x text-muted"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 更新步骤状态
            updateStepStatus(3);
        }

        // 生成报表
        function generateReport() {
            if (!selectedTemplate) {
                alert('请先选择报表模板');
                return;
            }
            
            // 更新步骤状态
            updateStepStatus(4);
            
            // 模拟报表生成
            alert('报表生成中，请稍候...');
            setTimeout(() => {
                alert('报表生成完成！');
                location.reload();
            }, 2000);
        }

        // 更新步骤状态
        function updateStepStatus(currentStep) {
            document.querySelectorAll('.step').forEach((step, index) => {
                step.classList.remove('active', 'completed');
                if (index + 1 < currentStep) {
                    step.classList.add('completed');
                } else if (index + 1 === currentStep) {
                    step.classList.add('active');
                }
            });
        }

        // 创建自定义报表
        function createCustomReport() {
            alert('自定义报表功能开发中...');
        }

        // 下载报表
        function downloadReport(id) {
            alert('正在下载报表...');
        }

        // 清空历史
        function clearHistory() {
            if (confirm('确认清空所有报表历史？')) {
                alert('报表历史已清空！');
                location.reload();
            }
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.report-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
