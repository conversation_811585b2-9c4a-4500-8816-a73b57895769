<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业列表 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar {
            background: linear-gradient(45deg, #667eea, #764ba2);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 0;
        }
        .sidebar-menu {
            list-style: none;
            padding: 1rem 0;
            margin: 0;
        }
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        .sidebar-menu a {
            display: block;
            padding: 0.8rem 1.5rem;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background: rgba(102, 126, 234, 0.1);
            border-left-color: #667eea;
            color: #667eea;
        }
        .sidebar-menu i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            padding: 2rem;
        }
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .search-filters {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .company-table {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 8px;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .company-type-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .type-material { background: rgba(102, 126, 234, 0.1); color: #667eea; }
        .type-construction { background: rgba(40, 167, 69, 0.1); color: #28a745; }
        .type-maintenance { background: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .type-inspection { background: rgba(220, 53, 69, 0.1); color: #dc3545; }
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .status-active { background: rgba(40, 167, 69, 0.1); color: #28a745; }
        .status-pending { background: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .status-inactive { background: rgba(108, 117, 125, 0.1); color: #6c757d; }
        .table th {
            border-top: none;
            font-weight: 600;
            color: #333;
            background: #f8f9fa;
        }
        .action-buttons .btn {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        .stats-cards {
            margin-bottom: 2rem;
        }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        .stats-label {
            color: #666;
            font-size: 0.9rem;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -250px;
                width: 250px;
                z-index: 1000;
                transition: left 0.3s ease;
            }
            .sidebar.show {
                left: 0;
            }
            .main-content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <button class="navbar-toggler d-lg-none" type="button" onclick="toggleSidebar()">
                <span class="navbar-toggler-icon"></span>
            </button>
            <a class="navbar-brand" href="../dashboard.html">
                <i class="fas fa-building"></i> 幕墙信息化平台
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user"></i> 个人资料</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-cog"></i> 系统设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar" id="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="list.html" class="active"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="../project/list.html"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="../curtain-wall/list.html"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="../statistics/overview.html"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="../system/users.html"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2><i class="fas fa-industry"></i> 企业管理</h2>
                                <p class="text-muted mb-0">管理平台注册企业信息</p>
                            </div>
                            <div>
                                <button class="btn btn-primary" onclick="window.location.href='edit.html'">
                                    <i class="fas fa-plus"></i> 新增企业
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 统计卡片 -->
                    <div class="row stats-cards">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-primary">156</div>
                                <div class="stats-label">企业总数</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-success">142</div>
                                <div class="stats-label">已审核</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-warning">12</div>
                                <div class="stats-label">待审核</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-danger">2</div>
                                <div class="stats-label">已禁用</div>
                            </div>
                        </div>
                    </div>

                    <!-- 搜索筛选 -->
                    <div class="search-filters">
                        <form class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">企业名称</label>
                                <input type="text" class="form-control" placeholder="请输入企业名称">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">企业类型</label>
                                <select class="form-select">
                                    <option value="">全部类型</option>
                                    <option value="1">幕墙材料企业</option>
                                    <option value="2">幕墙施工企业</option>
                                    <option value="3">既有幕墙维修企业</option>
                                    <option value="4">既有幕墙检查服务企业</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">状态</label>
                                <select class="form-select">
                                    <option value="">全部状态</option>
                                    <option value="1">正常</option>
                                    <option value="2">待审核</option>
                                    <option value="0">已禁用</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">地区</label>
                                <select class="form-select">
                                    <option value="">全部地区</option>
                                    <option value="pudong">浦东新区</option>
                                    <option value="huangpu">黄浦区</option>
                                    <option value="jingan">静安区</option>
                                    <option value="xuhui">徐汇区</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    <button type="reset" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo"></i> 重置
                                    </button>
                                    <button type="button" class="btn btn-outline-success" onclick="exportData()">
                                        <i class="fas fa-download"></i> 导出
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 企业列表 -->
                    <div class="company-table">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>企业名称</th>
                                        <th>企业类型</th>
                                        <th>法定代表人</th>
                                        <th>联系人</th>
                                        <th>联系电话</th>
                                        <th>状态</th>
                                        <th>注册时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-industry fa-2x text-primary"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">上海某幕墙材料有限公司</div>
                                                    <small class="text-muted">91310000123456789X</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="company-type-badge type-material">幕墙材料企业</span></td>
                                        <td>张三</td>
                                        <td>李四</td>
                                        <td>021-12345678</td>
                                        <td><span class="status-badge status-active">正常</span></td>
                                        <td>2024-01-15</td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewCompany(1)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="editCompany(1)">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" onclick="manageQualification(1)">
                                                    <i class="fas fa-certificate"></i> 资质
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-hammer fa-2x text-success"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">上海某建筑幕墙工程有限公司</div>
                                                    <small class="text-muted">91310000987654321Y</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="company-type-badge type-construction">幕墙施工企业</span></td>
                                        <td>王五</td>
                                        <td>赵六</td>
                                        <td>021-87654321</td>
                                        <td><span class="status-badge status-active">正常</span></td>
                                        <td>2024-01-20</td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewCompany(2)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="editCompany(2)">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" onclick="manageQualification(2)">
                                                    <i class="fas fa-certificate"></i> 资质
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-tools fa-2x text-warning"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">上海某幕墙维修服务有限公司</div>
                                                    <small class="text-muted">91310000456789123Z</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="company-type-badge type-maintenance">既有幕墙维修企业</span></td>
                                        <td>孙七</td>
                                        <td>周八</td>
                                        <td>021-45678912</td>
                                        <td><span class="status-badge status-pending">待审核</span></td>
                                        <td>2024-02-01</td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewCompany(3)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="approveCompany(3)">
                                                    <i class="fas fa-check"></i> 审核
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="rejectCompany(3)">
                                                    <i class="fas fa-times"></i> 拒绝
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-search fa-2x text-danger"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">上海某幕墙检测技术有限公司</div>
                                                    <small class="text-muted">91310000789123456A</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="company-type-badge type-inspection">既有幕墙检查服务企业</span></td>
                                        <td>吴九</td>
                                        <td>郑十</td>
                                        <td>021-78912345</td>
                                        <td><span class="status-badge status-active">正常</span></td>
                                        <td>2024-01-25</td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewCompany(4)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="editCompany(4)">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" onclick="manageQualification(4)">
                                                    <i class="fas fa-certificate"></i> 资质
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <nav aria-label="企业列表分页">
                            <ul class="pagination justify-content-center">
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1">上一页</a>
                                </li>
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                <li class="page-item"><a class="page-link" href="#">3</a></li>
                                <li class="page-item">
                                    <a class="page-link" href="#">下一页</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 切换侧边栏
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('show');
        }

        // 查看企业详情
        function viewCompany(id) {
            window.location.href = `detail.html?id=${id}`;
        }

        // 编辑企业
        function editCompany(id) {
            window.location.href = `edit.html?id=${id}`;
        }

        // 管理企业资质
        function manageQualification(id) {
            window.location.href = `qualification.html?id=${id}`;
        }

        // 审核企业
        function approveCompany(id) {
            if (confirm('确认审核通过该企业？')) {
                alert('企业审核通过！');
                location.reload();
            }
        }

        // 拒绝企业
        function rejectCompany(id) {
            const reason = prompt('请输入拒绝原因：');
            if (reason) {
                alert('企业审核已拒绝！');
                location.reload();
            }
        }

        // 导出数据
        function exportData() {
            alert('正在导出企业数据...');
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stats-card, .search-filters, .company-table');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
