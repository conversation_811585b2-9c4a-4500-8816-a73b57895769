<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>幕墙信息化平台原型导航</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .prototype-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem 0;
            margin-bottom: 3rem;
        }
        .prototype-title {
            color: white;
            text-align: center;
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 0.5rem;
        }
        .prototype-subtitle {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 1.1rem;
            margin-bottom: 0;
        }
        .nav-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: none;
        }
        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        .nav-card-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #667eea;
        }
        .nav-card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }
        .nav-card-desc {
            color: #666;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
        .nav-links {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .nav-links li {
            margin-bottom: 0.5rem;
        }
        .nav-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        .nav-links a:hover {
            color: #764ba2;
            text-decoration: underline;
        }
        .nav-links i {
            width: 20px;
            margin-right: 8px;
        }
        .footer {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem 0;
            margin-top: 3rem;
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
        }
        .tech-stack {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        .tech-stack h5 {
            color: white;
            margin-bottom: 1rem;
        }
        .tech-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.9rem;
            margin: 0.2rem;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="prototype-header">
        <div class="container">
            <h1 class="prototype-title">
                <i class="fas fa-building"></i>
                幕墙信息化平台
            </h1>
            <p class="prototype-subtitle">上海市装饰装修行业协会 - 系统原型导航</p>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- 用户认证模块 -->
            <div class="col-lg-6 col-md-6 mb-4">
                <div class="nav-card">
                    <div class="text-center">
                        <i class="fas fa-user-shield nav-card-icon"></i>
                    </div>
                    <h3 class="nav-card-title text-center">用户认证模块</h3>
                    <p class="nav-card-desc">用户登录、注册、权限管理等功能页面</p>
                    <ul class="nav-links">
                        <li><a href="login.html"><i class="fas fa-sign-in-alt"></i>用户登录</a></li>
                        <li><a href="register.html"><i class="fas fa-user-plus"></i>用户注册</a></li>
                        <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i>系统首页</a></li>
                    </ul>
                </div>
            </div>

            <!-- 企业管理模块 -->
            <div class="col-lg-6 col-md-6 mb-4">
                <div class="nav-card">
                    <div class="text-center">
                        <i class="fas fa-industry nav-card-icon"></i>
                    </div>
                    <h3 class="nav-card-title text-center">企业管理模块</h3>
                    <p class="nav-card-desc">企业信息、资质、人员、业绩管理功能</p>
                    <ul class="nav-links">
                        <li><a href="company/list.html"><i class="fas fa-list"></i>企业列表</a></li>
                        <li><a href="company/detail.html"><i class="fas fa-info-circle"></i>企业详情</a></li>
                        <li><a href="company/edit.html"><i class="fas fa-edit"></i>企业编辑</a></li>
                        <li><a href="company/qualification.html"><i class="fas fa-certificate"></i>资质管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 项目管理模块 -->
            <div class="col-lg-6 col-md-6 mb-4">
                <div class="nav-card">
                    <div class="text-center">
                        <i class="fas fa-project-diagram nav-card-icon"></i>
                    </div>
                    <h3 class="nav-card-title text-center">项目管理模块</h3>
                    <p class="nav-card-desc">项目信息、进度跟踪、质量管理功能</p>
                    <ul class="nav-links">
                        <li><a href="project/list.html"><i class="fas fa-list"></i>项目列表</a></li>
                        <li><a href="project/detail.html"><i class="fas fa-info-circle"></i>项目详情</a></li>
                        <li><a href="project/edit.html"><i class="fas fa-edit"></i>项目编辑</a></li>
                        <li><a href="project/progress.html"><i class="fas fa-tasks"></i>进度管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 既有幕墙管理模块 -->
            <div class="col-lg-6 col-md-6 mb-4">
                <div class="nav-card">
                    <div class="text-center">
                        <i class="fas fa-building-columns nav-card-icon"></i>
                    </div>
                    <h3 class="nav-card-title text-center">既有幕墙管理</h3>
                    <p class="nav-card-desc">既有幕墙信息、检查记录、维修管理</p>
                    <ul class="nav-links">
                        <li><a href="curtain-wall/list.html"><i class="fas fa-list"></i>幕墙列表</a></li>
                        <li><a href="curtain-wall/detail.html"><i class="fas fa-info-circle"></i>幕墙详情</a></li>
                        <li><a href="curtain-wall/inspection.html"><i class="fas fa-search"></i>检查记录</a></li>
                        <li><a href="curtain-wall/maintenance.html"><i class="fas fa-tools"></i>维修记录</a></li>
                    </ul>
                </div>
            </div>

            <!-- 统计分析模块 -->
            <div class="col-lg-6 col-md-6 mb-4">
                <div class="nav-card">
                    <div class="text-center">
                        <i class="fas fa-chart-bar nav-card-icon"></i>
                    </div>
                    <h3 class="nav-card-title text-center">统计分析模块</h3>
                    <p class="nav-card-desc">数据统计、图表展示、报表生成功能</p>
                    <ul class="nav-links">
                        <li><a href="statistics/overview.html"><i class="fas fa-chart-pie"></i>统计概览</a></li>
                        <li><a href="statistics/company-stats.html"><i class="fas fa-chart-line"></i>企业统计</a></li>
                        <li><a href="statistics/project-stats.html"><i class="fas fa-chart-area"></i>项目统计</a></li>
                        <li><a href="statistics/reports.html"><i class="fas fa-file-alt"></i>报表生成</a></li>
                    </ul>
                </div>
            </div>

            <!-- 系统管理模块 -->
            <div class="col-lg-6 col-md-6 mb-4">
                <div class="nav-card">
                    <div class="text-center">
                        <i class="fas fa-cogs nav-card-icon"></i>
                    </div>
                    <h3 class="nav-card-title text-center">系统管理模块</h3>
                    <p class="nav-card-desc">用户管理、角色权限、系统设置功能</p>
                    <ul class="nav-links">
                        <li><a href="system/users.html"><i class="fas fa-users"></i>用户管理</a></li>
                        <li><a href="system/roles.html"><i class="fas fa-user-tag"></i>角色管理</a></li>
                        <li><a href="system/settings.html"><i class="fas fa-cog"></i>系统设置</a></li>
                        <li><a href="system/logs.html"><i class="fas fa-history"></i>操作日志</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 技术栈说明 -->
        <div class="row">
            <div class="col-12">
                <div class="tech-stack">
                    <h5><i class="fas fa-code"></i> 技术栈</h5>
                    <div>
                        <span class="tech-badge">HTML5</span>
                        <span class="tech-badge">CSS3</span>
                        <span class="tech-badge">Bootstrap 5</span>
                        <span class="tech-badge">JavaScript</span>
                        <span class="tech-badge">jQuery</span>
                        <span class="tech-badge">Chart.js</span>
                        <span class="tech-badge">Font Awesome</span>
                        <span class="tech-badge">响应式设计</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <p><i class="fas fa-info-circle"></i> 本原型仅用于演示系统界面和交互流程，不包含后端逻辑</p>
            <p><i class="fas fa-envelope"></i> 联系邮箱: <EMAIL> | <i class="fas fa-phone"></i> 联系电话: 021-12345678</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.nav-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // 添加点击统计
        document.querySelectorAll('.nav-links a').forEach(link => {
            link.addEventListener('click', function(e) {
                console.log('访问页面:', this.href);
                // 这里可以添加访问统计逻辑
            });
        });
    </script>
</body>
</html>
