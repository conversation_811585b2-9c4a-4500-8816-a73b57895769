# 用户注册和认证流程图

## 用户注册流程

```mermaid
flowchart TD
    A[用户访问注册页面] --> B[选择用户类型]
    B --> C{用户类型}
    C -->|幕墙材料企业| D[填写企业基本信息]
    C -->|幕墙施工企业| D
    C -->|既有幕墙维修企业| D
    C -->|既有幕墙检查服务企业| D
    C -->|协会管理员| E[管理员邀请注册]
    
    D --> F[填写联系人信息]
    F --> G[上传企业资质文件]
    G --> H[设置登录密码]
    H --> I[发送手机验证码]
    I --> J[输入验证码]
    J --> K{验证码正确?}
    K -->|否| I
    K -->|是| L[提交注册申请]
    
    E --> M[填写管理员信息]
    M --> N[设置登录密码]
    N --> O[管理员审核]
    O --> P{审核通过?}
    P -->|否| Q[注册失败]
    P -->|是| R[账号激活]
    
    L --> S[系统初步验证]
    S --> T{信息完整性检查}
    T -->|不完整| U[提示补充信息]
    U --> D
    T -->|完整| V[进入人工审核]
    V --> W[协会管理员审核]
    W --> X{审核结果}
    X -->|拒绝| Y[发送拒绝通知]
    X -->|通过| Z[账号激活]
    X -->|需补充材料| AA[发送补充材料通知]
    AA --> G
    
    Y --> BB[注册流程结束]
    Z --> CC[发送激活通知]
    R --> CC
    CC --> DD[用户可正常登录]
    Q --> BB
    
    style A fill:#e1f5fe
    style DD fill:#e8f5e8
    style BB fill:#ffebee
    style Q fill:#ffebee
```

## 用户登录认证流程

```mermaid
flowchart TD
    A[用户访问登录页面] --> B[输入用户名/手机号]
    B --> C[输入密码]
    C --> D[点击登录]
    D --> E{账号状态检查}
    E -->|账号不存在| F[提示账号不存在]
    E -->|账号被禁用| G[提示账号被禁用]
    E -->|账号正常| H[验证密码]
    
    H --> I{密码正确?}
    I -->|否| J[记录失败次数]
    J --> K{失败次数 >= 5?}
    K -->|是| L[账号临时锁定]
    K -->|否| M[提示密码错误]
    M --> C
    L --> N[发送解锁验证码]
    
    I -->|是| O[检查是否需要二次验证]
    O --> P{需要二次验证?}
    P -->|是| Q[发送短信验证码]
    P -->|否| R[生成JWT Token]
    
    Q --> S[用户输入验证码]
    S --> T{验证码正确?}
    T -->|否| U[提示验证码错误]
    U --> S
    T -->|是| R
    
    R --> V[获取用户权限信息]
    V --> W[生成用户会话]
    W --> X[返回登录成功]
    X --> Y[跳转到用户首页]
    
    F --> Z[登录失败]
    G --> Z
    N --> AA[账号解锁流程]
    AA --> AB[输入手机验证码]
    AB --> AC{验证码正确?}
    AC -->|否| AB
    AC -->|是| AD[解锁账号]
    AD --> AE[重置失败次数]
    AE --> C
    
    style A fill:#e1f5fe
    style Y fill:#e8f5e8
    style Z fill:#ffebee
    style L fill:#fff3e0
```

## 权限验证流程

```mermaid
flowchart TD
    A[用户发起请求] --> B[提取JWT Token]
    B --> C{Token存在?}
    C -->|否| D[返回未登录错误]
    C -->|是| E[验证Token有效性]
    E --> F{Token有效?}
    F -->|否| G[返回Token无效错误]
    F -->|是| H[解析用户信息]
    H --> I[获取用户权限列表]
    I --> J[检查请求资源权限]
    J --> K{有访问权限?}
    K -->|否| L[返回权限不足错误]
    K -->|是| M[记录访问日志]
    M --> N[允许访问资源]
    
    D --> O[跳转登录页面]
    G --> O
    L --> P[显示权限不足页面]
    N --> Q[返回请求结果]
    
    style A fill:#e1f5fe
    style Q fill:#e8f5e8
    style O fill:#ffebee
    style P fill:#ffebee
```

## 密码重置流程

```mermaid
flowchart TD
    A[用户点击忘记密码] --> B[输入用户名/手机号]
    B --> C[点击获取验证码]
    C --> D{账号存在?}
    D -->|否| E[提示账号不存在]
    D -->|是| F[发送短信验证码]
    F --> G[用户输入验证码]
    G --> H{验证码正确?}
    H -->|否| I[提示验证码错误]
    I --> G
    H -->|是| J[进入密码重置页面]
    J --> K[输入新密码]
    K --> L[确认新密码]
    L --> M{两次密码一致?}
    M -->|否| N[提示密码不一致]
    N --> K
    M -->|是| O[验证密码强度]
    O --> P{密码强度符合要求?}
    P -->|否| Q[提示密码强度不够]
    Q --> K
    P -->|是| R[更新用户密码]
    R --> S[清除所有登录会话]
    S --> T[发送密码修改通知]
    T --> U[跳转登录页面]
    
    E --> V[重置流程结束]
    U --> W[用户重新登录]
    
    style A fill:#e1f5fe
    style W fill:#e8f5e8
    style V fill:#ffebee
```

## 会话管理流程

```mermaid
flowchart TD
    A[用户登录成功] --> B[创建用户会话]
    B --> C[设置会话过期时间]
    C --> D[将会话信息存储到Redis]
    D --> E[返回会话Token给客户端]
    
    E --> F[用户正常使用系统]
    F --> G[每次请求携带Token]
    G --> H[验证Token有效性]
    H --> I{Token有效?}
    I -->|是| J[刷新会话过期时间]
    I -->|否| K[清除无效会话]
    
    J --> L[继续处理请求]
    K --> M[返回会话过期错误]
    M --> N[跳转登录页面]
    
    L --> O{用户主动退出?}
    O -->|是| P[清除会话信息]
    O -->|否| Q[检查会话是否过期]
    Q -->|过期| P
    Q -->|未过期| F
    
    P --> R[删除Redis中的会话]
    R --> S[清除客户端Token]
    S --> T[跳转登录页面]
    
    style A fill:#e1f5fe
    style L fill:#e8f5e8
    style N fill:#ffebee
    style T fill:#fff3e0
```

## 流程说明

### 1. 用户注册流程特点
- **多类型用户支持**: 支持不同类型企业用户注册
- **资质审核机制**: 企业资质需要人工审核
- **手机验证**: 确保联系方式真实有效
- **管理员特殊流程**: 管理员通过邀请方式注册

### 2. 登录认证流程特点
- **多种登录方式**: 支持用户名、手机号登录
- **安全防护**: 密码错误次数限制，账号锁定机制
- **二次验证**: 重要操作需要短信验证
- **JWT Token**: 无状态认证方案

### 3. 权限验证流程特点
- **统一权限控制**: 所有请求统一权限验证
- **细粒度权限**: 基于资源的权限控制
- **访问日志**: 记录所有访问操作
- **友好错误处理**: 明确的错误提示

### 4. 密码重置流程特点
- **安全验证**: 短信验证码确认身份
- **密码强度检查**: 确保密码安全性
- **会话清除**: 重置密码后清除所有会话
- **通知机制**: 密码修改后发送通知

### 5. 会话管理流程特点
- **Redis存储**: 高性能会话存储
- **自动续期**: 活跃用户自动延长会话
- **安全退出**: 主动退出清除所有会话信息
- **过期处理**: 自动清理过期会话
