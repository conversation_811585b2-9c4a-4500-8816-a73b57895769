# 系统整体架构图

```mermaid
graph TB
    subgraph "用户层"
        U1[幕墙材料企业用户]
        U2[幕墙施工企业用户]
        U3[既有幕墙维修企业用户]
        U4[既有幕墙检查服务企业用户]
        U5[协会管理员用户]
    end

    subgraph "接入层"
        LB[负载均衡器]
        CDN[CDN内容分发网络]
        WAF[Web应用防火墙]
    end

    subgraph "前端层"
        WEB[Web前端应用]
        MOBILE[移动端应用]
        ADMIN[管理后台]
    end

    subgraph "网关层"
        API_GW[API网关]
        AUTH[认证授权服务]
        RATE[限流控制]
    end

    subgraph "应用服务层"
        USER_SVC[用户管理服务]
        COMPANY_SVC[企业管理服务]
        PROJECT_SVC[项目管理服务]
        CURTAIN_SVC[既有幕墙管理服务]
        ASSOC_SVC[协会管理服务]
        REPORT_SVC[统计分析服务]
        SYSTEM_SVC[系统管理服务]
    end

    subgraph "数据服务层"
        CACHE[Redis缓存]
        SEARCH[Elasticsearch搜索]
        FILE[文件存储服务]
        MSG[消息队列]
    end

    subgraph "数据存储层"
        DB_MASTER[主数据库]
        DB_SLAVE[从数据库]
        BACKUP[备份存储]
        LOG_DB[日志数据库]
    end

    subgraph "外部集成"
        GOV_API[政府监管平台API]
        THIRD_AUTH[第三方认证系统]
        SMS[短信服务]
        EMAIL[邮件服务]
    end

    subgraph "监控运维"
        MONITOR[系统监控]
        LOG[日志收集]
        ALERT[告警系统]
        DEPLOY[部署系统]
    end

    %% 用户访问流程
    U1 --> LB
    U2 --> LB
    U3 --> LB
    U4 --> LB
    U5 --> LB

    %% 接入层处理
    LB --> CDN
    LB --> WAF
    CDN --> WEB
    WAF --> WEB

    %% 前端到网关
    WEB --> API_GW
    MOBILE --> API_GW
    ADMIN --> API_GW

    %% 网关层处理
    API_GW --> AUTH
    API_GW --> RATE
    AUTH --> USER_SVC

    %% 服务调用
    API_GW --> USER_SVC
    API_GW --> COMPANY_SVC
    API_GW --> PROJECT_SVC
    API_GW --> CURTAIN_SVC
    API_GW --> ASSOC_SVC
    API_GW --> REPORT_SVC
    API_GW --> SYSTEM_SVC

    %% 数据服务调用
    USER_SVC --> CACHE
    COMPANY_SVC --> CACHE
    PROJECT_SVC --> SEARCH
    CURTAIN_SVC --> FILE
    REPORT_SVC --> CACHE
    SYSTEM_SVC --> MSG

    %% 数据库访问
    USER_SVC --> DB_MASTER
    COMPANY_SVC --> DB_MASTER
    PROJECT_SVC --> DB_MASTER
    CURTAIN_SVC --> DB_MASTER
    ASSOC_SVC --> DB_MASTER
    REPORT_SVC --> DB_SLAVE
    SYSTEM_SVC --> DB_MASTER

    %% 数据备份
    DB_MASTER --> BACKUP
    DB_SLAVE --> BACKUP

    %% 外部集成
    ASSOC_SVC --> GOV_API
    AUTH --> THIRD_AUTH
    SYSTEM_SVC --> SMS
    SYSTEM_SVC --> EMAIL

    %% 监控运维
    API_GW --> MONITOR
    USER_SVC --> LOG
    COMPANY_SVC --> LOG
    PROJECT_SVC --> LOG
    CURTAIN_SVC --> LOG
    ASSOC_SVC --> LOG
    REPORT_SVC --> LOG
    SYSTEM_SVC --> LOG
    
    MONITOR --> ALERT
    LOG --> LOG_DB

    classDef userClass fill:#e1f5fe
    classDef frontendClass fill:#f3e5f5
    classDef serviceClass fill:#e8f5e8
    classDef dataClass fill:#fff3e0
    classDef externalClass fill:#fce4ec

    class U1,U2,U3,U4,U5 userClass
    class WEB,MOBILE,ADMIN frontendClass
    class USER_SVC,COMPANY_SVC,PROJECT_SVC,CURTAIN_SVC,ASSOC_SVC,REPORT_SVC,SYSTEM_SVC serviceClass
    class DB_MASTER,DB_SLAVE,CACHE,SEARCH,FILE dataClass
    class GOV_API,THIRD_AUTH,SMS,EMAIL externalClass
```

## 架构说明

### 1. 用户层
- **幕墙材料企业用户**: 产品展示、客户管理
- **幕墙施工企业用户**: 项目管理、供应商查找
- **既有幕墙维修企业用户**: 维修记录管理
- **既有幕墙检查服务企业用户**: 检查报告录入
- **协会管理员用户**: 行业管理、统计分析

### 2. 接入层
- **负载均衡器**: 分发用户请求，提高系统可用性
- **CDN**: 静态资源缓存，提升访问速度
- **WAF**: Web应用防火墙，防护安全攻击

### 3. 前端层
- **Web前端应用**: 主要业务功能界面
- **移动端应用**: 移动设备访问支持
- **管理后台**: 系统管理和配置界面

### 4. 网关层
- **API网关**: 统一API入口，路由分发
- **认证授权服务**: 用户身份验证和权限控制
- **限流控制**: API访问频率限制

### 5. 应用服务层
- **用户管理服务**: 用户注册、登录、权限管理
- **企业管理服务**: 企业信息、资质管理
- **项目管理服务**: 项目信息、进度跟踪
- **既有幕墙管理服务**: 幕墙检查、维修记录
- **协会管理服务**: 会员管理、行业监管
- **统计分析服务**: 数据统计、报表生成
- **系统管理服务**: 系统配置、日志管理

### 6. 数据服务层
- **Redis缓存**: 热点数据缓存，提升性能
- **Elasticsearch**: 全文搜索和复杂查询
- **文件存储服务**: 文档、图片等文件存储
- **消息队列**: 异步处理和系统解耦

### 7. 数据存储层
- **主数据库**: 核心业务数据存储
- **从数据库**: 读写分离，提升查询性能
- **备份存储**: 数据备份和灾难恢复
- **日志数据库**: 系统日志和审计数据

### 8. 外部集成
- **政府监管平台API**: 数据上报和同步
- **第三方认证系统**: 企业资质验证
- **短信服务**: 验证码和通知发送
- **邮件服务**: 邮件通知和报告发送

### 9. 监控运维
- **系统监控**: 性能监控和健康检查
- **日志收集**: 集中日志管理
- **告警系统**: 异常情况告警
- **部署系统**: 自动化部署和发布

## 架构特点

1. **高可用性**: 通过负载均衡、数据库主从复制等保证系统可用性
2. **高性能**: 使用缓存、CDN、读写分离等技术提升性能
3. **可扩展性**: 微服务架构支持水平扩展
4. **安全性**: 多层安全防护，数据加密传输
5. **可维护性**: 服务解耦，便于独立开发和部署
