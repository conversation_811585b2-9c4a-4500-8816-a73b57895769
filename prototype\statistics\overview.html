<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计概览 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Microsoft YaHei', sans-serif; }
        .navbar { background: linear-gradient(45deg, #667eea, #764ba2); box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .sidebar { background: white; min-height: calc(100vh - 76px); box-shadow: 2px 0 10px rgba(0,0,0,0.1); padding: 0; }
        .sidebar-menu { list-style: none; padding: 1rem 0; margin: 0; }
        .sidebar-menu li { margin-bottom: 0.5rem; }
        .sidebar-menu a { display: block; padding: 0.8rem 1.5rem; color: #333; text-decoration: none; transition: all 0.3s ease; border-left: 3px solid transparent; }
        .sidebar-menu a:hover, .sidebar-menu a.active { background: rgba(102, 126, 234, 0.1); border-left-color: #667eea; color: #667eea; }
        .sidebar-menu i { width: 20px; margin-right: 10px; }
        .main-content { padding: 2rem; }
        .page-header { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .stats-card { background: white; border-radius: 15px; padding: 1.5rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); text-align: center; transition: transform 0.3s ease; margin-bottom: 2rem; }
        .stats-card:hover { transform: translateY(-5px); }
        .stats-icon { width: 60px; height: 60px; border-radius: 15px; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; color: white; margin: 0 auto 1rem; }
        .stats-number { font-size: 2rem; font-weight: 700; margin-bottom: 0.5rem; }
        .stats-label { color: #666; font-size: 0.9rem; }
        .chart-card { background: white; border-radius: 15px; padding: 1.5rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); margin-bottom: 2rem; }
        .chart-title { font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem; color: #333; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.html"><i class="fas fa-building"></i> 幕墙信息化平台</a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="../company/list.html"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="../project/list.html"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="../curtain-wall/list.html"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="overview.html" class="active"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="../system/users.html"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2><i class="fas fa-chart-bar"></i> 统计分析概览</h2>
                                <p class="text-muted mb-0">行业数据统计分析和趋势展示</p>
                            </div>
                            <div>
                                <button class="btn btn-outline-primary me-2" onclick="window.location.href='company-stats.html'">
                                    <i class="fas fa-industry"></i> 企业统计
                                </button>
                                <button class="btn btn-outline-success me-2" onclick="window.location.href='project-stats.html'">
                                    <i class="fas fa-project-diagram"></i> 项目统计
                                </button>
                                <button class="btn btn-primary" onclick="window.location.href='reports.html'">
                                    <i class="fas fa-file-alt"></i> 生成报表
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 核心指标卡片 -->
                    <div class="row">
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="stats-icon" style="background: linear-gradient(45deg, #667eea, #764ba2);">
                                    <i class="fas fa-industry"></i>
                                </div>
                                <div class="stats-number text-primary">156</div>
                                <div class="stats-label">注册企业总数</div>
                                <small class="text-success"><i class="fas fa-arrow-up"></i> 较上月增长 8.5%</small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="stats-icon" style="background: linear-gradient(45deg, #28a745, #20c997);">
                                    <i class="fas fa-project-diagram"></i>
                                </div>
                                <div class="stats-number text-success">89</div>
                                <div class="stats-label">在建项目数量</div>
                                <small class="text-success"><i class="fas fa-arrow-up"></i> 较上月增长 12.3%</small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="stats-icon" style="background: linear-gradient(45deg, #ffc107, #fd7e14);">
                                    <i class="fas fa-building-columns"></i>
                                </div>
                                <div class="stats-number text-warning">234</div>
                                <div class="stats-label">既有幕墙建筑</div>
                                <small class="text-info"><i class="fas fa-minus"></i> 与上月持平</small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="stats-icon" style="background: linear-gradient(45deg, #dc3545, #e83e8c);">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="stats-number text-danger">12</div>
                                <div class="stats-label">安全隐患预警</div>
                                <small class="text-danger"><i class="fas fa-arrow-down"></i> 较上月减少 25%</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- 企业注册趋势 -->
                        <div class="col-lg-8">
                            <div class="chart-card">
                                <h5 class="chart-title">
                                    <i class="fas fa-chart-line"></i> 企业注册趋势
                                </h5>
                                <div style="height: 300px;">
                                    <canvas id="registrationTrendChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- 企业类型分布 -->
                        <div class="col-lg-4">
                            <div class="chart-card">
                                <h5 class="chart-title">
                                    <i class="fas fa-chart-pie"></i> 企业类型分布
                                </h5>
                                <div style="height: 300px;">
                                    <canvas id="companyTypeChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- 项目投资规模 -->
                        <div class="col-lg-6">
                            <div class="chart-card">
                                <h5 class="chart-title">
                                    <i class="fas fa-chart-bar"></i> 项目投资规模分布
                                </h5>
                                <div style="height: 300px;">
                                    <canvas id="investmentChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- 安全等级分布 -->
                        <div class="col-lg-6">
                            <div class="chart-card">
                                <h5 class="chart-title">
                                    <i class="fas fa-shield-alt"></i> 既有幕墙安全等级分布
                                </h5>
                                <div style="height: 300px;">
                                    <canvas id="safetyLevelChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 地区分布热力图 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="chart-card">
                                <h5 class="chart-title">
                                    <i class="fas fa-map-marked-alt"></i> 上海市各区企业分布
                                </h5>
                                <div class="row text-center">
                                    <div class="col-md-2 mb-3">
                                        <div class="border rounded p-3">
                                            <h4 class="text-primary">28</h4>
                                            <small>浦东新区</small>
                                        </div>
                                    </div>
                                    <div class="col-md-2 mb-3">
                                        <div class="border rounded p-3">
                                            <h4 class="text-success">22</h4>
                                            <small>黄浦区</small>
                                        </div>
                                    </div>
                                    <div class="col-md-2 mb-3">
                                        <div class="border rounded p-3">
                                            <h4 class="text-warning">18</h4>
                                            <small>静安区</small>
                                        </div>
                                    </div>
                                    <div class="col-md-2 mb-3">
                                        <div class="border rounded p-3">
                                            <h4 class="text-info">16</h4>
                                            <small>徐汇区</small>
                                        </div>
                                    </div>
                                    <div class="col-md-2 mb-3">
                                        <div class="border rounded p-3">
                                            <h4 class="text-secondary">14</h4>
                                            <small>长宁区</small>
                                        </div>
                                    </div>
                                    <div class="col-md-2 mb-3">
                                        <div class="border rounded p-3">
                                            <h4 class="text-dark">58</h4>
                                            <small>其他区域</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 企业注册趋势图表
        const registrationCtx = document.getElementById('registrationTrendChart').getContext('2d');
        new Chart(registrationCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                datasets: [{
                    label: '新注册企业',
                    data: [12, 15, 18, 22, 25, 28, 32, 35, 38, 42, 45, 48],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } }
            }
        });

        // 企业类型分布图表
        const companyTypeCtx = document.getElementById('companyTypeChart').getContext('2d');
        new Chart(companyTypeCtx, {
            type: 'doughnut',
            data: {
                labels: ['幕墙材料', '幕墙施工', '维修企业', '检查服务'],
                datasets: [{
                    data: [45, 52, 32, 27],
                    backgroundColor: ['#667eea', '#28a745', '#ffc107', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { position: 'bottom' } }
            }
        });

        // 项目投资规模图表
        const investmentCtx = document.getElementById('investmentChart').getContext('2d');
        new Chart(investmentCtx, {
            type: 'bar',
            data: {
                labels: ['1000万以下', '1000-5000万', '5000万-1亿', '1亿-5亿', '5亿以上'],
                datasets: [{
                    label: '项目数量',
                    data: [25, 35, 18, 8, 3],
                    backgroundColor: '#667eea'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } }
            }
        });

        // 安全等级分布图表
        const safetyCtx = document.getElementById('safetyLevelChart').getContext('2d');
        new Chart(safetyCtx, {
            type: 'pie',
            data: {
                labels: ['安全', '关注', '隐患'],
                datasets: [{
                    data: [198, 24, 12],
                    backgroundColor: ['#28a745', '#ffc107', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { position: 'bottom' } }
            }
        });

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stats-card, .chart-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
