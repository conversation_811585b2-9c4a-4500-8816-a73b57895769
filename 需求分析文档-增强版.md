# 上海市装饰装修行业协会幕墙信息化平台需求分析文档（增强版）

## 1. 项目概述

### 1.1 项目背景
- **项目名称**: 上海市装饰装修行业协会幕墙信息化平台建设服务
- **建设单位**: 上海市装饰装修行业协会
- **项目预算**: 45万元人民币
- **服务期限**: 2025年-2026年
- **项目目标**: 开发一个功能完善的数字信息库系统，主要服务于幕墙材料企业、幕墙施工企业、既有幕墙维修企业、既有幕墙检查服务企业及协会管理人员

### 1.2 技术架构
- 采用微服务架构，确保系统可扩展性
- 前后端分离的web应用方式设计
- 容器化部署（Docker+K8s）实现快速扩展

### 1.3 平台整体模块划分
系统采用模块化设计，分为5个核心功能模块，既独立运行，又通过统一用户中心和数据平台实现互联互通：

1. **协会管理系统** - 面向行业协会管理人员，提供会员、资质、申请表单、流程审批等核心管理功能
2. **企业管理系统** - 服务幕墙企业，涵盖组织、项目、供需等业务管理
3. **从业人员系统** - 为施工/管理人员提供职业发展平台
4. **消费者服务系统** - 连接终端消费群体用户与服务提供商企业
5. **综合门户站点** - 统一入口和行业信息枢纽

## 2. 协会管理端功能需求

### 2.1 组织架构管理
- **FR-001**: 可配置的协会组织架构管理
- **FR-002**: 支持多级部门设置（如：市装饰协会建筑幕墙专业委员会 → 上海市既有建筑幕墙现场检查组认定管理工作组）
- **FR-003**: 职位管理和人员分配
- **FR-004**: 角色权限分配和管理

### 2.2 入会管理体系
#### 2.2.1 入会前管理
- **FR-005**: 潜在会员库管理
- **FR-006**: 定向营销和入会引导
- **FR-007**: 会籍适配分析（主会/分会，等级评估）

#### 2.2.2 入会流程管理
- **FR-008**: 标准化入会流程（申请→审核→建档）
- **FR-009**: 工作流和审批流程管理
- **FR-010**: 会籍管理（主会/分会管理架构）
- **FR-011**: 分级体系和动态权限分配机制
- **FR-012**: 申请进度详情页面（条件审核→材料复核→会费确认→审批）

#### 2.2.3 入会后管理
- **FR-013**: 会员状态管理（已入会、续费、离会）
- **FR-014**: 自动化续费提醒和流程
- **FR-015**: 会员历史数据归档机制

### 2.3 资质认定体系
- **FR-016**: 资质认定体系分组管理（检查组申请认定、检查组人员申请、维修企业认定申请、维修管理人员申请、维修施工人员申请）
- **FR-017**: 资质预审核+专家评审双机制
- **FR-018**: 资质认定申请列表和过滤视图
- **FR-019**: 资质认定详情页面和工作流程
- **FR-020**: 资质有效期管理和失效提醒

### 2.4 申请表单模板引擎
- **FR-021**: 可视化表单模板设计器
- **FR-022**: 模板字段配置（必填、选填、自动填写、关联表统计）
- **FR-023**: 模板分组管理（协会标准、企业规范、从业申请、消费者约定）
- **FR-024**: 模板版本控制和发布管理
- **FR-025**: 表单数据自动带入功能

### 2.5 行业服务模块
#### 2.5.1 标准体系管理
- **FR-026**: 企业资质证书列表管理
- **FR-027**: 人员资质证书列表管理
- **FR-028**: 人员职称列表管理
- **FR-029**: 资质标准版本管理和修订

#### 2.5.2 课程培训管理
- **FR-030**: 课程培训项目管理
- **FR-031**: 培训报名和人数管理
- **FR-032**: 课程材料和考核管理
- **FR-033**: 证书评估和发放
- **FR-034**: 培训工作流程管理（课前→课中→课后）

### 2.6 流程控制管理
- **FR-035**: 状态机模式工作流程管理
- **FR-036**: 可视化流程设计器
- **FR-037**: 多级审批配置
- **FR-038**: 条件分支和自动触发规则
- **FR-039**: 流程状态管理和变更历史

### 2.7 角色权限管理
- **FR-040**: 基于RBAC的权限管理
- **FR-041**: 11种角色权限配置（系统管理员、协会管理员、企业管理员、检查组技术负责人、检查组组长、现场检查人员、维修技术负责人、维修项目经理、维修安全员、维修施工人员、从业人员、普通用户）
- **FR-042**: 数据权限控制（企业级、检查组级、项目级）
- **FR-043**: 权限变更流程和审计

### 2.8 通知管理
- **FR-044**: 多渠道通知系统（微信公众号、短信、邮件、站内消息）
- **FR-045**: 通知模板管理和变量替换
- **FR-046**: 事件触发通知机制
- **FR-047**: 通知发送记录和统计

### 2.9 系统支持功能
- **FR-048**: 操作日志审计
- **FR-049**: 数据备份恢复
- **FR-050**: 系统配置管理（系统级、模块级、企业级）

## 3. 企业管理端功能需求

### 3.1 企业注册登录
- **FR-051**: 营业执照OCR识别注册
- **FR-052**: 临时密码和密码变更
- **FR-053**: 手机号绑定和变更

### 3.2 管理人员账号
- **FR-054**: 主账号邀请链接机制
- **FR-055**: 角色指定和权限分配

### 3.3 公司信息管理
- **FR-056**: 工商系统接入和信息提取
- **FR-057**: 公司信息展示和维护

### 3.4 组织架构管理
- **FR-058**: 多级部门组织管理
- **FR-059**: 岗位设置和员工配备
- **FR-060**: 组织架构可视化展示

### 3.5 模板和申请管理
- **FR-061**: 协会模板展示和企业自定义模板
- **FR-062**: 入会、资质、证书申请流程
- **FR-063**: 申请资格条件核对
- **FR-064**: 检查组成员指定

### 3.6 项目管理
- **FR-065**: 项目录入和模板字段管理
- **FR-066**: 项目列表按模板分组展示
- **FR-067**: 项目详情和参与人管理
- **FR-068**: 项目角色分配和材料管理
- **FR-069**: 项目状态和工作流程

### 3.7 文档管理
- **FR-070**: 合同/证书电子归档
- **FR-071**: 文档标签和分组管理
- **FR-072**: 文档检索功能

### 3.8 权限控制
- **FR-073**: 企业内部资源访问控制
- **FR-074**: 角色权限管理

## 4. 从业人员端功能需求

### 4.1 注册登录
- **FR-075**: 身份证OCR识别注册
- **FR-076**: 密码管理和账号绑定

### 4.2 文档管理
- **FR-077**: 个人文档管理和标签分组

### 4.3 简历管理
- **FR-078**: 多版本简历编辑
- **FR-079**: 公开/保密模式设置
- **FR-080**: 简历关联资质和证书

### 4.4 资质证书管理
- **FR-081**: 已完成资质证书管理
- **FR-082**: 可申报资质展示和申报流程
- **FR-083**: 课程培训报名
- **FR-084**: 配合填报功能

### 4.5 企业项目参与
- **FR-085**: 加入企业和项目管理
- **FR-086**: 参与项目列表和详情查看
- **FR-087**: 项目角色数据管理

## 5. 消费者端功能需求

### 5.1 用户注册登录
- **FR-088**: 手机号注册和短信验证码登录
- **FR-089**: 微信绑定功能

### 5.2 需求服务流程
- **FR-090**: 标准化需求模板发布
- **FR-091**: 装修流程工作流管理（前期准备→设计→施工→验收）
- **FR-092**: 需求发布列表和状态管理
- **FR-093**: 需求详情和进度跟踪

## 6. 综合门户功能需求

### 6.1 统一登录入口
- **FR-094**: 多角色登录注册入口
- **FR-095**: 角色识别和页面跳转

### 6.2 资源检索服务
- **FR-096**: 找需求功能
- **FR-097**: 找材料功能
- **FR-098**: 找企业功能
- **FR-099**: 角色化检索配置

### 6.3 行业动态
- **FR-100**: 公示通报管理
- **FR-101**: 明星企业展示
- **FR-102**: 标杆项目展示
- **FR-103**: 优秀专家展示
- **FR-104**: 学习培训报名

## 7. 非功能需求

### 7.1 性能需求
- **NFR-001**: 系统响应时间不超过3秒
- **NFR-002**: 支持并发用户数不少于500人
- **NFR-003**: 系统可用性达到99.5%以上

### 7.2 安全需求
- **NFR-004**: 敏感字段加密（身份证、手机号、邮箱等）
- **NFR-005**: SSL/TLS加密传输
- **NFR-006**: 操作审计日志保留180天
- **NFR-007**: 多重认证机制

### 7.3 可扩展性需求
- **NFR-008**: 微服务架构支持水平扩展
- **NFR-009**: 容器化部署支持
- **NFR-010**: 模块化设计便于功能扩展

### 7.4 兼容性需求
- **NFR-011**: 支持主流浏览器
- **NFR-012**: 响应式设计支持移动端
- **NFR-013**: 私有化部署支持

## 8. 技术架构需求

### 8.1 服务架构
- **TR-001**: 微服务架构设计
- **TR-002**: API网关统一入口
- **TR-003**: 主/从数据库和缓存数据库
- **TR-004**: 容器化部署（Docker+K8s）

### 8.2 实施计划
- **TR-005**: 需求确认（4-6周）
- **TR-006**: 核心开发（8-12周）
- **TR-007**: 系统集成（4-6周）
- **TR-008**: 试点运行（4-6周）
- **TR-009**: 正式上线（2-4周）

### 8.3 运维保障
- **TR-010**: 用户量监控
- **TR-011**: 服务健康检查
- **TR-012**: 实时告警机制
- **TR-013**: 故障响应承诺（8小时内在线支持）

---

**文档版本**: V2.0（增强版）
**编制日期**: 2024年12月
**基于**: 信息化平台建设服务方案.md
**更新内容**: 融合服务方案中的详细业务需求和技术架构
