<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目进度管理 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Microsoft YaHei', sans-serif; }
        .navbar { background: linear-gradient(45deg, #667eea, #764ba2); box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .sidebar { background: white; min-height: calc(100vh - 76px); box-shadow: 2px 0 10px rgba(0,0,0,0.1); padding: 0; }
        .sidebar-menu { list-style: none; padding: 1rem 0; margin: 0; }
        .sidebar-menu li { margin-bottom: 0.5rem; }
        .sidebar-menu a { display: block; padding: 0.8rem 1.5rem; color: #333; text-decoration: none; transition: all 0.3s ease; border-left: 3px solid transparent; }
        .sidebar-menu a:hover, .sidebar-menu a.active { background: rgba(102, 126, 234, 0.1); border-left-color: #667eea; color: #667eea; }
        .sidebar-menu i { width: 20px; margin-right: 10px; }
        .main-content { padding: 2rem; }
        .page-header { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .progress-card { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .timeline { position: relative; padding-left: 2rem; }
        .timeline::before { content: ''; position: absolute; left: 1rem; top: 0; bottom: 0; width: 2px; background: #e9ecef; }
        .timeline-item { position: relative; margin-bottom: 2rem; }
        .timeline-marker { position: absolute; left: -2rem; top: 0.5rem; width: 1rem; height: 1rem; border-radius: 50%; background: #667eea; border: 3px solid white; box-shadow: 0 0 0 3px #e9ecef; }
        .timeline-marker.completed { background: #28a745; }
        .timeline-marker.current { background: #ffc107; }
        .timeline-content { background: #f8f9fa; border-radius: 10px; padding: 1.5rem; margin-left: 1rem; }
        .progress-bar-container { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 1rem 0; }
        .progress-bar { height: 100%; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 10px; transition: width 0.3s ease; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; }
        .btn-primary { background: linear-gradient(45deg, #667eea, #764ba2); border: none; border-radius: 8px; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3); }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.html"><i class="fas fa-building"></i> 幕墙信息化平台</a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="../company/list.html"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="list.html" class="active"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="../curtain-wall/list.html"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="../statistics/overview.html"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="../system/users.html"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb mb-2">
                                        <li class="breadcrumb-item"><a href="list.html">项目管理</a></li>
                                        <li class="breadcrumb-item"><a href="detail.html?id=1">项目详情</a></li>
                                        <li class="breadcrumb-item active">进度管理</li>
                                    </ol>
                                </nav>
                                <h2><i class="fas fa-tasks"></i> 项目进度管理</h2>
                                <p class="text-muted mb-0">上海某商业大厦幕墙工程</p>
                            </div>
                            <div>
                                <button class="btn btn-outline-secondary me-2" onclick="window.location.href='detail.html?id=1'">
                                    <i class="fas fa-arrow-left"></i> 返回详情
                                </button>
                                <button class="btn btn-primary" onclick="addProgress()">
                                    <i class="fas fa-plus"></i> 新增进度
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 项目总体进度 -->
                    <div class="progress-card">
                        <h4><i class="fas fa-chart-line"></i> 项目总体进度</h4>
                        <div class="progress-bar-container">
                            <div class="progress-bar" style="width: 65%">65%</div>
                        </div>
                        <div class="row text-center">
                            <div class="col-3">
                                <small class="text-muted">开工日期</small>
                                <div class="fw-bold">2024-01-15</div>
                            </div>
                            <div class="col-3">
                                <small class="text-muted">计划完工</small>
                                <div class="fw-bold">2024-12-31</div>
                            </div>
                            <div class="col-3">
                                <small class="text-muted">已用时间</small>
                                <div class="fw-bold">45天</div>
                            </div>
                            <div class="col-3">
                                <small class="text-muted">剩余时间</small>
                                <div class="fw-bold">320天</div>
                            </div>
                        </div>
                    </div>

                    <!-- 进度时间轴 -->
                    <div class="progress-card">
                        <h4><i class="fas fa-clock"></i> 进度时间轴</h4>
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker completed"></div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5>基础施工</h5>
                                            <p class="mb-1"><strong>计划时间:</strong> 2024年1月15日 - 2024年3月1日</p>
                                            <p class="mb-1"><strong>实际时间:</strong> 2024年1月15日 - 2024年3月5日</p>
                                            <p class="mb-1"><strong>负责人:</strong> 张工程师</p>
                                            <p class="mb-0"><strong>备注:</strong> 因天气原因延期5天，已完成验收</p>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-success mb-2">已完成</span>
                                            <div class="fw-bold text-success">100%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="timeline-item">
                                <div class="timeline-marker current"></div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5>主体结构施工</h5>
                                            <p class="mb-1"><strong>计划时间:</strong> 2024年3月1日 - 2024年8月1日</p>
                                            <p class="mb-1"><strong>实际时间:</strong> 2024年3月5日 - 进行中</p>
                                            <p class="mb-1"><strong>负责人:</strong> 李工程师</p>
                                            <p class="mb-0"><strong>备注:</strong> 按计划进行，预计7月底完成</p>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-warning mb-2">进行中</span>
                                            <div class="fw-bold text-warning">75%</div>
                                        </div>
                                    </div>
                                    <div class="progress-bar-container mt-2">
                                        <div class="progress-bar" style="width: 75%; font-size: 0.8rem;">75%</div>
                                    </div>
                                </div>
                            </div>

                            <div class="timeline-item">
                                <div class="timeline-marker"></div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5>幕墙安装</h5>
                                            <p class="mb-1"><strong>计划时间:</strong> 2024年8月1日 - 2024年11月1日</p>
                                            <p class="mb-1"><strong>实际时间:</strong> 待开始</p>
                                            <p class="mb-1"><strong>负责人:</strong> 王工程师</p>
                                            <p class="mb-0"><strong>备注:</strong> 等待主体结构完成</p>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-secondary mb-2">未开始</span>
                                            <div class="fw-bold text-muted">0%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="timeline-item">
                                <div class="timeline-marker"></div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5>装饰装修</h5>
                                            <p class="mb-1"><strong>计划时间:</strong> 2024年11月1日 - 2024年12月15日</p>
                                            <p class="mb-1"><strong>实际时间:</strong> 待开始</p>
                                            <p class="mb-1"><strong>负责人:</strong> 赵工程师</p>
                                            <p class="mb-0"><strong>备注:</strong> 内外装修同步进行</p>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-secondary mb-2">未开始</span>
                                            <div class="fw-bold text-muted">0%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="timeline-item">
                                <div class="timeline-marker"></div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h5>竣工验收</h5>
                                            <p class="mb-1"><strong>计划时间:</strong> 2024年12月15日 - 2024年12月31日</p>
                                            <p class="mb-1"><strong>实际时间:</strong> 待开始</p>
                                            <p class="mb-1"><strong>负责人:</strong> 项目经理</p>
                                            <p class="mb-0"><strong>备注:</strong> 最终验收和交付</p>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-secondary mb-2">未开始</span>
                                            <div class="fw-bold text-muted">0%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增进度模态框 -->
    <div class="modal fade" id="progressModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus"></i> 新增进度</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="progressForm">
                        <div class="mb-3">
                            <label class="form-label">进度名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" required>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">计划开始日期</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">计划完成日期</label>
                                <input type="date" class="form-control">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">负责人</label>
                                <input type="text" class="form-control">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">完成率(%)</label>
                                <input type="number" class="form-control" min="0" max="100" value="0">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">进度描述</label>
                            <textarea class="form-control" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveProgress()">保存进度</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 新增进度
        function addProgress() {
            const modal = new bootstrap.Modal(document.getElementById('progressModal'));
            modal.show();
        }

        // 保存进度
        function saveProgress() {
            alert('进度信息保存成功！');
            bootstrap.Modal.getInstance(document.getElementById('progressModal')).hide();
            location.reload();
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.progress-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
