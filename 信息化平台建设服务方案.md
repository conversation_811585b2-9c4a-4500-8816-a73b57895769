## 项目概述

本项目旨在开发一个功能完善的数字信息库系统，主要服务于上海市装饰装修行业协会的幕墙材料企业、幕墙施工企业、 既有幕墙维修企业、既有幕墙检查服务企业及协会管理人员。

采用微服务架构，确保系统可扩展性;前后端分离的web应用方式设计;容器化部署（Docker+K8s）实现快速扩展。

当前文档前部分是需求分析内容，文档结尾是技术服务方案内容。

## 一、平台整体模块划分

为了更好的服务各个角色，系统采用模块化设计，分为5个核心功能模块，分别服务于建筑幕墙行业的不同参与方；
这样可以快速的为各个服务做扩展，又让各模块既独立运行，还可以通过统一用户中心和数据平台实现互联互通，杜绝数据孤岛，高内聚低耦合。

1. **协会管理系统** ： 面向行业协会管理人员，提供会员、资质、申请表单、流程审批等核心管理功能
2. **企业管理系统** ： 服务幕墙企业，涵盖组织、项目、供需等业务管理
3. **从业人员系统** ： 为施工/管理人员提供职业发展平台
4. **消费者服务系统** ： 连接终端消费群体用户与服务提供商企业
5. **综合门户站点** ： 统一入口和行业信息枢纽


## 二、协会管理端

### 1、协会端管理员账号开具由系统管理员开放

### 2、协会管理员账号可开设其他管理账号并赋予相应的角色权限

- 主账号发起邀请链接后可加入协会
- 角色由主账号指定

### 3、组织架构

可以调整配置，协会端的组织架构；比如添加如下组织部门、设置的职位以及职位所安排的哪些人员:

- 市装饰协会建筑幕墙专业委员会
  - 上海市既有建筑幕墙现场检查组认定管理工作组
    - 组长职位
      - 人员A
    - 组员职位
      - 人员a
      - 人员b

- 上海市装饰装修行业协会建筑幕墙专业委员会材料分会
  - 会长职位
    - X会长
  - 副会长职位
    - Y副会长
  - 理事
    - 理事a
    - 理事b

### 4、入会

#### 入会前

建立潜在会员库，数据来源与已经注册的但没有入会企业，针对这些企业可设计定向营销和入会引导如邀请入会等活动。

| 企业名  | 适合会籍 | 适合等级 | 动作      |
| ------- | -------- | -------- | --------- |
| xxx公司 | 主会     | 常务理事 | 邀请/排除 |
| xxx学校 | 分会     | 常务理事 | 邀请/排除 |
| xxx学校 | 分会     | 常务理事 | 邀请/排除 |

#### 入会中

已经通过标准化入会流程（申请→审核→建档），申请的企业数据，通过标准化的 **工作流、审批流程**管理入会的进度。 

**会籍管理**：主会/分会管理架构设计可以使用不同的**申请模板**填报入会； 

**分级体系**：`申请模板`可以设置关联会员级别的字段用于触发动态权限分配机制；对不同级别的会员分配不同的权限；

| 企业名  | 申请会籍 | 申请等级 | 动作         |
| ------- | -------- | -------- | ------------ |
| xxx公司 | 主会     | 常务理事 | 申请进度详情 |
| xxx公司 | 分会     | 常务理事 | 申请进度详情 |

#### 申请进度详情页面

- 帮助文档: 入会须知等帮助文档

- 工作流程: 条件审核 -> 材料复核 -> 会费确认 -> 审批

1. 默认展示对应进度下的内容
2. 条件审核：展示企业详情，处理人，处理情况
3. 材料复核：展示申请的相关材料，如若缺少，提醒添加，处理人，处理情况
4. 会费确认：展示会费缴纳情况，处理人，处理情况
5. 审批：展示 `审批流`相关参与人和对应的处理情况

#### 入会后

| 企业名  | 会籍 | 等级     | 动作     |
| ------- | ---- | -------- | -------- |
| xxx公司 | 主会 | 常务理事 | 续费提醒 |
| yyy公司 | 分会 | 常务理事 | 活动邀请 |
| zzz公司 | 分会 | 常务理事 | 离会归档 |

**状态管理设计**：

- **已入会**：活跃会员服务与管理

- **续 费**：自动化续费提醒和流程

- **离 会**：会员历史数据归档机制


### 5、资质认定体系

以评审标准和认定方法的制定为依据，对不同的评审认定情况进行认定评审工作；
可采用资质预审核+专家评审双机制进行审核，使用`工作流程`、`审批流程`即可以灵活的进行配制管理审核认定办法。

其中包含:

#### 资质认定体系分组

1. 检查组申请认定
2. 检查组人员申请
3. 维修企业认定申请
4. 维修管理人员申请
5. 维修施工人员申请

#### 资质认定申请列表

提供常用的表格搜索，过滤，以及常用过滤视图的保存；方便使用。

| 企业名  | 认定资质     | 认定情况 | 状态     |
| ------- | ------------ | -------- | -------- |
| xxx公司 | 幕墙检查组   | 详情     | 认定有效 |
| yyy公司 | 维修企业     | 详情     | 认定有效 |
| zzz公司 | 维修企业     | 详情     | 认定失效 |
| zzz公司 | 维修管理人员 | 详情     | 认定有效 |
| zzz公司 | 维修施工人员 | 详情     | 认定有效 |
| zzz公司 | 维修施工人员 | 详情     | 认定失效 |
| zzz公司 | 维修施工人员 | 详情     | 认定失效 |

#### 幕墙检查组认定详情页面

- 依据`表单模板`渲染展示具体内容

- 帮助文档: 认定帮助文档展示，辅助工作人员参考认定标准

- 工作流程: 结合各个场景的`工作流程`的定义展开差异化的 `条件审核` -> `材料复核` -> `审批`等工作流程

1. 默认展示对应进度下的内容
2. 条件审核：展示申请详情（企业及人员情况），当前的处理人，处理情况，是否通过
3. 材料符合：申请相关材料展示，缺少提醒，当前阶段的处理人，处理情况，是否通过
4. 审批: 展示关联的`审批流`参与人和对应审批情况，是否通过


### 6、申请表单模板引擎

为了支持`入会`，`资质认定`等工作中使用到的各种常用申请表单，推出申请表单模板，客制化需要的字段内容；
比如需要提交那些内容，内容的要求是怎样的，是否必须填写，最小数量要求等。

列表页面可以新建模板，对模板进行字段的调整；
**模板字段中可以设置关联表统计提交自动带入等功能**。

#### 模板分组

如下为常用的模板分组：

- 协会标准
- 企业规范
- 从业申请
- 消费者约定
- 需要其他分组

#### 模板列表

| 模板名称               | 创建人 | 是否发布 | 详情             |
| ---------------------- | ------ | -------- | ---------------- |
| 主会入会申请表         | Andy   | 已经停用 | 点击打开详情页面 |
| 主会入会申请表         | Andy   | 草稿     | [详情页面]()     |
| 主会入会申请表         | Andy   | 使用中   | 详情页面         |
| 分会入会申请表         | Bob    | 使用中   | 详情页面         |
| 维修管理人员申请表     | Andy   | 草稿     | [详情页面]()     |
| 维修管理人员申请表     | Andy   | 使用中   | [详情页面]()     |
| **消费者需求发布模板** | Andy   | 使用中   | [详情页面]()     |


#### 主会入会申请表模板

| 字段     | 要求                             |
| -------- | -------------------------------- |
| 企业名称 | 自动填写(登录身份识别后自动填写) |
| 注册地址 | 自动填写(登录身份识别后自动填写) |
| 办公地址 | 必须填写                         |
| 邮政编码 | 必须填写                         |
| ...      | ...                              |
| 备注     | 选择填写                         |
| 填表人   | 必须填写                         |
| 填表日期 | 程序自动生成                     |

#### 材料企业分会入会申请表模板

| 字段     | 要求                             |
| -------- | -------------------------------- |
| 企业名称 | 自动填写(登录身份识别后自动填写) |
| 所在地区 | 自动填写(登录身份识别后自动填写) |
| ...      | ...                              |
| 协会意见 |                                  |
| 登记人   |                                  |
| 登记日期 |                                  |


#### 上海市既有建筑幕墙现场检查组人员申请表

| 字段         | 要求                                 |
| ------------ | ------------------------------------ |
| 姓名         | 登录自动填写                         |
| 粘 贴 照 片  | 附件材料收集                         |
| ...          | ...                                  |
| 个人工作简历 | 由个人简历内容自动带入(提交端可编辑) |
| 填表时间     | 程序自动生成                         |
| 申请人签字   | 签字弹窗                             |


#### 上海市既有建筑幕墙现场检查组申请表

| 字段                   | 要求                     |
| ---------------------- | ------------------------ |
| 企业名称               | 企业资质自动带入         |
| 施工资质               |                          |
| 设计资质               |                          |
| ...                    | ...                      |
| 检查组成员名单         | (**关联表统计自动填入**) |
| 企业现有的检测设备情况 |                          |


#### 上海市既有建筑幕墙维修企业认定申请表


| 字段                   | 要求                                                   |
| ---------------------- | ------------------------------------------------------ |
| 企业名称               |                                                        |
| 幕墙施工资质           | (**企业资质自动带入**)                                 |
| 幕墙设计资质           | (**企业资质自动带入**)                                 |
| ...                    | ...                                                    |
| 近两年共承担维修项目数 | 项 (**企业项目自动带入**)                              |
| 维修部门人员合计       | 人数（**由关联模板提交表统计自动填入**）               |
| 企业承诺               | 本企业承诺本表及其他附件资料内容均为真实、有效、合法的 |
| 单位盖章及法人代表签字 |                                                        |
| 注                     | 申报企业提供营业执照和资质证书扫描件                   |


#### 既有建筑幕墙维修管理人员申请表模板

| 字段         | 要求                                                                 |
| ------------ | -------------------------------------------------------------------- |
| 姓名         | 自动填写                                                             |
| 身份证号码   | 自动填写                                                             |
| 所在单位     | 自动填写（登录身份识别）                                             |
| 文化程度     |                                                                      |
| ....         | ...                                                                  |
| 维修部门岗位 | 列表选择:维修技术负责人/维修项目经理/维修现场安全员/维修技术管理人员 |
| 电子邮箱     |                                                                      |
| 个人工作简历 |                                                                      |
| 填表时间     | （单位盖章）                                                         |
| 附件材料     | 上传照片                                                             |


#### 既有建筑幕墙维修施工人员申请表模板

| 字段         | 要求                                                |
| ------------ | --------------------------------------------------- |
| 所在单位     | 归属企业单位自动带入                                |
| 姓名         | 个人信息自动带入                                    |
| 身份证号码   | 个人信息自动带入                                    |
| 文化程度     | 个人简历自动带入                                    |
| 毕业学校     | 个人简历自动带入                                    |
| 专业工作年限 |                                                     |
| 工种         |                                                     |
| 上岗证书情况 |                                                     |
| 注：         | 1. 另附一张同底一寸相片 2. 本表格须同时提交电子文件 |

#### 上海市既有建筑幕墙维修工程项目表模板

| 字段               | 要求                      |
| ------------------ | ------------------------- |
| **企业名称**       | _________________________ |
| **项目名称**       | _________________________ |
| **项目地址**       | _________________________ |
| **产权人**         | _________________________ |
| **物业单位**       | _________________________ |
| **原竣工日期**     | _________________________ |
| **原施工单位**     | _________________________ |
| **建筑总高度**     | _________ m               |
| **幕墙高度**       | _________ m               |
| **幕墙总面积**     | _________ m²              |
| **维修面积**       | _________ m²              |
| **维修开工日期**   | _________________________ |
| **维修竣工日期**   | _________________________ |
| **工程造价**       | _________ 元              |
| **维修内容及部位** | _________________________ |
| **维修方法**       | _________________________ |
| **实际维修效果**   | _________________________ |
| **备注**           | _________________________ |


### 7、行业服务模块

**标准体系管理**：

- **资质、证书、职称列表**：详细记录各类资质种类，明确适用企业类型和人群, 指导企业和个人规划；支持修订以及版本的管理；
- **配合申请认定、审核**：为申请、认定表单、`工作流程`中的数据提供依赖；

#### 企业资质证书列表


| **资质名称**                     | **等级**     | **核心要求参数**                                                                                        | **适用范围**                                                  |
| -------------------------------- | ------------ | ------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------- |
| **建筑业企业资质证书**           | **三级**     | - 净资产≥4000万元<br>- 注册建造师≥12人（建筑专业≥9人）<br>- 近5年完成2类指定工程业绩                    | 中小型建筑工程（如8-11层民用建筑、单项合同额≤3000万元的工程） |
|                                  | **二级**     | - 净资产≥8000万元<br>- 注册建造师≥15人（建筑专业≥12人）<br>- 近5年完成中型复杂工程（如50米以上构筑物）  | 中型工程总承包（如25层以下建筑、高度100米以下构筑物）         |
|                                  | **一级**     | - 净资产≥1亿元<br>- 注册建造师≥30人（含一级建造师≥15人）<br>- 近5年完成大型复杂工程（如单跨24米钢结构） | 全国范围大型工程总承包（如超高层建筑、大型工业厂房）          |
| **建筑幕墙工程专业承包资质证书** | **二级**     | - 净资产≥500万元<br>- 注册建造师≥5人（建筑/结构专业≥4人）<br>- 近5年完成单体幕墙面积≤3000㎡工程         | 中小型幕墙施工（高度≤30米、单体面积≤3000㎡）                  |
|                                  | **一级**     | - 净资产≥2000万元<br>- 注册建造师≥6人（一级建造师≥3人）<br>- 近5年完成6项单体幕墙≥6000㎡工程            | 不限规模的幕墙工程（如超高层玻璃幕墙、异形曲面幕墙）          |
| **上海市建设工程材料备案证**     | **基础备案** | - 营业执照、产品检测报告（GB/T标准）<br>- 生产设备清单<br>- 有效期2年                                   | 本地普通工程材料供应（需满足基本安全与环保要求）              |
|                                  | **优选认证** | - 环保认证（如ISO 14001）<br>- 耐久性专项检测（10年寿命）<br>- 政府推荐目录准入资格                     | 政府/重点工程采购（如地铁、机场、医院等）                     |
| **上海市建筑幕墙材料备案证**     | **A类备案**  | - 幕墙材料防火等级A级<br>- 抗风压性能≥4kPa<br>- 提供全生命周期质量承诺书                                | 普通幕墙工程（适用于高度≤50米建筑）                           |
|                                  | **S类备案**  | - 防火等级A1级<br>- 抗风压性能≥6kPa<br>- 抗震性能检测报告<br>- 10年质保协议                             | 超高层/特殊幕墙工程（如高度≥100米、异形结构、地标建筑）       |


#### 人员资质证书列表


| **资质/证书名称**              | **等级**       | **核心要求**                                                                                 | **适用范围**                               |
| ------------------------------ | -------------- | -------------------------------------------------------------------------------------------- | ------------------------------------------ |
| **注册建造师执业资格证书**     | 二级           | - 建筑工程相关专业本科+3年经验<br>- 通过全国统一考试并注册<br>- 有效期3年（需继续教育）      | 中小型工程项目经理、幕墙维修技术负责人     |
|                                | 一级           | - 本科+5年经验或硕士+3年经验<br>- 通过全国统考并注册<br>- 有效期3年（需完成120学时继续教育） | 大型工程总承包项目经理、甲级项目技术负责人 |
| **建筑安全员证书（C证）**      | 基础级         | - 高中以上学历+安全培训考核合格<br>- 有效期3年（需复审）                                     | 施工现场普通安全员                         |
|                                | 高级安全工程师 | - 持C证5年以上+中级职称<br>- 通过安全工程师资格考试                                          | 大型项目安全总监、区域安全管理负责人       |
| **幕墙维修管理人员培训合格证** | 初级           | - 3年以上幕墙工程经验<br>- 参加48学时培训并通过考核<br>- 有效期2年                           | 中小型幕墙维修项目管理                     |
|                                | 高级           | - 持初级证满2年+主持完成3个维修项目<br>- 参加72学时进阶培训                                  | 复杂幕墙维修工程（如超高层、历史建筑）管理 |
| **幕墙维修施工人员培训合格证** | 普通施工员     | - 2年以上幕墙施工经验<br>- 参加40学时技能培训<br>- 有效期2年                                 | 常规幕墙维修作业（玻璃更换、密封胶修补）   |
|                                | 特种作业员     | - 持普通证1年+高空作业资格<br>- 通过专项技能考核（如吊篮操作）                               | 高空幕墙维修（≥50米）、异形幕墙拆卸施工    |
| **中级及以上专业技术职称证书** | 中级           | - 本科+5年经验/硕士+2年经验<br>- 通过职称评审（论文+业绩）<br>- 长期有效                     | 技术负责人、质量总监                       |
|                                | 高级           | - 中级职称满5年+重大技术成果<br>- 通过省级专家评审                                           | 企业总工程师、重大项目技术决策             |


#### 人员职称列表


| **职称名称**           | **等级**        | **核心要求**                                                             | **适用范围**                             |
| ---------------------- | --------------- | ------------------------------------------------------------------------ | ---------------------------------------- |
| **幕墙维修技术负责人** | 中级/高级       | - 建筑结构类/机械类专业<br>- 8年以上幕墙工程经验<br>- 注册建造师执业资格 | 幕墙维修项目技术管理、方案制定与审核     |
| **幕墙维修项目经理**   | 中级/注册建造师 | - 建筑工程相关专业<br>- 3年以上幕墙工程经验<br>- 具备项目管理能力        | 幕墙维修项目统筹与执行（中小型项目主导） |
| **现场安全员**         | C证（安全员）   | - 3年以上幕墙工程经验<br>- 安全培训合格<br>- 熟悉高空作业安全规范        | 施工现场安全管理、风险监控               |
| **维修管理人员**       | 初级/高级       | - 3年以上幕墙工程经验<br>- 管理培训合格<br>- 掌握维修流程与成本控制      | 维修团队协调、进度与质量管理             |
| **维修施工人员**       | 普通/特种作业员 | - 2年以上幕墙施工经验<br>- 技能培训合格<br>- 持有高空作业证（特种作业）  | 幕墙维修施工操作（常规/高空/异形作业）   |
| **初级职称**           | 初级            | - 大专学历+1年经验或中专学历+3年经验<br>- 通过初级职称评审               | 助理工程师、技术员（基础技术支持岗位）   |
| **中级职称**           | 中级            | - 初级职称+4年经验或硕士学历+2年经验<br>- 发表技术论文或完成项目成果     | 工程师、项目经理（技术决策与项目管理）   |
| **高级职称**           | 高级            | - 中级职称+5年经验<br>- 主持重大技术项目或获省级以上奖项                 | 高级工程师、技术专家（企业技术战略规划） |


#### 课程以及培训

提供课程培训相关的数据管理能力，高效运营课程培训活动。

##### 列表

展示当前所有`状态`的课程培训项目。

| 名称                 | 面向对象 | 报名开始时间 | 报名结束时间 | 培训课程目标 | 目前状态 | 详情     |
| -------------------- | -------- | ------------ | ------------ | ------------ | -------- | -------- |
| 幕墙维修管理人员培训 | 企业     | 2025-01-01   | 2025-01-30   | xxx          | 筹备     | [查看]() |
| 幕墙维修施工人员培训 | 企业     | 2025-01-01   | 2025-01-30   | xxx          | 筹备     | [查看]() |
| BIM技术培训          | 从业人员 | 2025-01-01   | 2025-01-30   | xxx          | 筹备     | [查看]() |
| 安全生产管理体系培训 | 企业     | 2025-01-01   | 2025-01-30   | xxx          | 筹备     | [查看]() |
| 消防安全知识培训     | 企业     | 2025-01-01   | 2025-01-30   | xxx          | 筹备     | [查看]() |


##### 详情页面

依据关于课程培训的`工作流程`中定义的内容，对点选课程培训详情内容进行相应状态的展示；

- 课程发布后可以展示具体内容
- 展示报名的人员企业
- 课程材料的准备、上传
- 考核的准备材料的上传
- 考核评估标准制定通知
- 证书评估、发放，发放后企业可在自己登录账号中查看

##### 触发`工作流`(可调整)

##### 课前

```mermaid
graph LR
A[课程发起] --> B[多级审批]
B -->|通过| C[资源筹备/课程发布]
B -->|驳回| A
C --> E[学员报名]
E --> F{人数达标?}
F -->|是| G[报名截止]
F -->|否| H[延期/取消]
H --> I[通知学员]
G -->|进入课中| J[开课准备]
```

##### 课中

```mermaid
graph LR
J[开课准备] --> K[培训实施]
K --> L[过程考核]
L -->|进入课后| M[认证评估]
```

##### 课后

```mermaid
graph LR
M[认证评估] --> N[证书发放]
N --> O[归档结项]
O --> P[企业查看证书]
```

### 8、流程控制管理

采用状态机模式实现灵活可控的工作流程管理，协助入会、表单申请等流程控制。

#### 例如资质申请与升级流程

```mermaid
graph LR
    A(企业提交资质申请) --> B{材料初审}
    B -->|通过| C[专家评审/现场核查]
    B -->|驳回| D[补正通知]
    C -->|通过| E[公示]
    C -->|驳回| D
    E --> F[颁发资质证书]
    F --> G[资质入库]
```

#### 例如证书续期与升级流程

```mermaid
graph LR
    A(证书到期前90天提醒) --> B[提交续期申请]
    B --> C{自动校验}
    C -->|继续教育学时达标| D[自动审批]
    C -->|学时不足| E[跳转培训平台]
    D --> F[生成电子证书]
    E --> G[完成学习] --> D
```

#### 例如职称评审流程

```mermaid
graph LR
    A(个人申报) --> B[企业初审]
    B --> C{材料合规性检查}
    C -->|通过| D[专家评审会]
    C -->|驳回| E[退回修改]
    D --> F[结果公示]
    F --> G[颁发职称证书]
```

#### 主要功能

1. 流程配置：
   - 可视化流程设计器
   - 多级审批配置
   - 条件分支配置
   - 自动触发规则

2. 状态管理：
   - 状态定义
   - 状态转换规则
   - 状态变更历史
   - 状态通知设置

3. 权限控制：
   - 基于角色的流程操作权限
   - 字段级状态控制
   - 操作级权限控制

#### 数据模型设计

```mermaid
erDiagram
    WORKFLOW_DEFINITION ||--o{ WORKFLOW_STATE : has
    WORKFLOW_DEFINITION ||--o{ WORKFLOW_TRANSITION : has
    WORKFLOW_INSTANCE ||--o{ WORKFLOW_HISTORY : has
    WORKFLOW_INSTANCE ||--o{ WORKFLOW_TASK : has
    
    WORKFLOW_DEFINITION {
        string id PK
        string name
        string description
        string entity_type
        string version
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    WORKFLOW_STATE {
        string id PK
        string workflow_id FK
        string name
        string code
        string description
        boolean is_initial
        boolean is_final
        string style
        int sort_order
    }
    
    WORKFLOW_TRANSITION {
        string id PK
        string workflow_id FK
        string from_state FK
        string to_state FK
        string name
        string description
        string condition_expression
        string action_expression
        boolean is_automatic
        int priority
    }
    
    WORKFLOW_INSTANCE {
        string id PK
        string workflow_id FK
        string entity_id
        string current_state FK
        string status
        timestamp created_at
        timestamp updated_at
    }
    
    WORKFLOW_HISTORY {
        string id PK
        string instance_id FK
        string from_state FK
        string to_state FK
        string transition_id FK
        string operator
        string comment
        timestamp created_at
    }
    
    WORKFLOW_TASK {
        string id PK
        string instance_id FK
        string transition_id FK
        string assignee
        string status
        timestamp created_at
        timestamp completed_at
    }
```


### 9、角色定义权限划分

根据RBAC实现方案和业务要求，系统角色权限划分如，具体落地配置情况按实际需求调整：

| 角色名称         | 角色描述             | 数据权限           | 操作权限                                           | 对应业务模块                                 |
| ---------------- | -------------------- | ------------------ | -------------------------------------------------- | -------------------------------------------- |
| 系统管理员       | 拥有系统全部权限     | 所有数据           | 所有操作                                           | 全部模块                                     |
| 协会管理员       | 管理协会相关业务     | 协会管理相关数据   | 企业管理、资质审核、检查组管理、维修企业审核       | 企业管理、资质管理、检查组管理、维修企业管理 |
| 企业管理员       | 管理本企业信息及人员 | 本企业数据         | 企业信息维护、人员管理、检查申请提交、维修申请提交 | 企业管理、人员管理、检查管理、维修管理       |
| 检查组技术负责人 | 负责检查组技术工作   | 本检查组相关数据   | 审核检查报告、技术问题处理                         | 检查管理                                     |
| 检查组组长       | 组织现场检查工作     | 本检查组相关数据   | 组织检查、签发检查报告、分配检查任务               | 检查管理                                     |
| 现场检查人员     | 执行现场检查         | 分配的检查任务数据 | 填写检查记录、上传检查结果                         | 检查管理                                     |
| 维修技术负责人   | 制定维修方案         | 本企业维修项目数据 | 制定维修方案、解决技术问题                         | 维修管理                                     |
| 维修项目经理     | 组织维修施工         | 负责的维修项目数据 | 项目进度管理、人员调配、材料申请                   | 维修管理                                     |
| 维修安全员       | 监督维修现场安全     | 负责项目的安全数据 | 安全检查、安全报告提交                             | 维修管理                                     |
| 维修施工人员     | 执行具体维修工作     | 分配的工作任务数据 | 维修记录填写、完工确认                             | 维修管理                                     |
| 从业人员         | 个人业务操作         | 个人相关数据       | 个人信息维护、业务申报                             | 个人中心                                     |
| 普通用户         | 基本查询权限         | 公开信息           | 信息查询                                           | 信息公开                                     |


#### 角色权限说明

1. **数据权限**：
   - 企业级角色只能访问本企业数据
   - 检查组角色只能访问本检查组相关数据
   - 项目级角色只能访问负责项目的数据

2. **特殊权限控制**：
   - 敏感操作需要二次验证
   - 数据导出需要审批

3. **权限继承**：
   - 高级角色自动拥有下级角色的权限

#### 权限变更流程

1. 角色权限变更需提交申请
2. 由系统管理员审批
3. 变更记录存入审计日志
4. 相关用户收到权限变更通知


### 10、通知管理

基于当前设计方案和需求，"通知管理"模块设计方案如下：

#### 系统架构

```mermaid
graph TD
    A[业务系统] -->|触发事件| B(通知服务)
    B --> C[消息队列]
    C --> D{分发路由}
    D -->|公众号| E[微信服务]
    D -->|短信| F[短信网关]
    D -->|邮件| G[邮件服务器]
    D -->|站内| H[消息中心]
```
#### 通知触发引擎

- 事件监听：监听业务系统关键事件（如申请提交、状态变更等）
- 触发规则：配置事件与通知类型的映射关系
- 条件过滤：基于业务规则过滤需要发送的通知

#### 多渠道适配层

- 统一接口：提供标准化的通知发送接口
- 渠道适配：对接各平台API（微信公众号、短信网关、邮件服务器）
- 失败重试：实现自动重试机制

#### 模板管理系统

- 变量替换：支持动态内容插入
- 版本控制：模板修改历史追溯

#### 发送记录与统计

- 发送日志：记录所有通知发送详情
- 状态跟踪：实时监控通知状态
- 统计分析：成功率、到达率等指标

### 11、系统支持功能

#### 操作日志审计

- 记录所有关键操作，比如`入会`审批通过记录，各种申请通过记录；
- 日志字段：操作人、操作时间、操作类型、操作对象、IP地址、操作详情；
- 日志表设计：

```mermaid
erDiagram
    SYSTEM_AUDIT_LOG {
        bigint id PK
        varchar(36) user_id
        timestamp operation_time
        varchar(50) operation_type
        varchar(50) target_type
        varchar(36) target_id
        varchar(50) ip_address
        text operation_details
        varchar(20) status
    }
```

#### 数据备份恢复

- 每日全量备份 + 每小时增量备份
- 备份策略：本地存储 + 云端异地备份
- 保留周期：全量备份30天，增量备份7天
- 恢复流程：验证->选择恢复点->执行恢复->验证数据

#### 系统配置管理

指定需要配置的项目，增加配置功能，当配置调整后系统对应模块会有相应的变动；比如针对企业是否开放某些模块。

##### 配置层级

| 层级   | 作用范围     | 优先级 | 示例                       |
| ------ | ------------ | ------ | -------------------------- |
| 系统级 | 全局生效     | 最低   | 系统名称、LOGO、默认语言   |
| 模块级 | 特定功能模块 | 中     | 入会流程配置、资质审核规则 |
| 企业级 | 单个企业     | 最高   | 企业自定义字段、审批流程   |


##### 配置存储设计

```mermaid
erDiagram
    CONFIG_GROUP ||--o{ CONFIG_ITEM : contains
    CONFIG_VALUE ||--o{ CONFIG_ITEM : has_value
    
    CONFIG_GROUP {
        string id PK
        string name
        string description
        string level
        timestamp created_at
        timestamp updated_at
    }
    
    CONFIG_ITEM {
        string id PK
        string group_id FK
        string key
        string name
        string description
        string data_type
        string default_value
        boolean is_sensitive
        timestamp created_at
        timestamp updated_at
    }
    
    CONFIG_VALUE {
        string id PK
        string item_id FK
        string scope_id
        string value
        string version
        string created_by
        timestamp created_at
        timestamp effective_from
        timestamp effective_to
    }
```

##### 配置变更流程

1. 变更申请：填写变更原因、影响范围；
2. 审批流程：根据配置级别触发不同审批；
3. 版本记录：自动生成新版本并保留历史；
4. 生效控制：支持立即生效或定时生效；
5. 变更通知：相关用户自动收到变更通知。

## 三、企业端

### 1、企业注册/登录

- 营业执照上传OCR识别注册并登录；
- 登录临时密码，变更登录密码;
- 手机号绑定/变更等常用功能。

### 2、管理人员账号

- 主账号发起邀请链接后可加入企业；
- 角色由主账号指定。

### 3、公司信息

- 工商系统接入，提取主要信息并展示在公司信息内容中。

### 4、组织架构

1. 多级部门组织
2. 岗位设置
3. 员工配备

### 5、模板列表

这里展示协会模板(开放的且在使用中的)，企业用户可以自行配置模板形成自有模板；

#### 入会、资质、证书申请 

围绕协会安排、企业自发的申请表进行申请，参与资料整理、人员安排、项目录入、评审等信息化处理流程。
这里会展示，当前登录企业可能参与的申请，比如:

- 如果当前企业没有入会那么展示：`入会登记表`；
- `上海市既有建筑幕墙现场检查组申请表`；
- `上海市既有建筑幕墙维修企业认定申请表`；

登录用户可以打开当前申请表，并进行初步核对企业的申请资格条件是否符合要求；
申请表展示字段内容为协会端在模板中配置的对应表单；
在确认符合要求后，可以指定检查组成员，成员来自于企业组织架构内的员工。

### 6、项目管理

项目录入数据来自于模板库中的模板规定的字段信息；录入项目可配合协会各种`申请`中对项目资质的要求数据进行匹配审核。

#### 项目列表

按模板分组展示，详情点击查看打开弹出页面，展示详细内容，可以编辑详细内容等常用功能。

| 名称   | 地址        | 产权人 | 物业单位 | 原竣工日期 | ... | 详情 |
| ------ | ----------- | ------ | -------- | ---------- | --- | ---- |
| xx项目 | xxx路1204号 | xx国际 | xx物业   | 2005-10-24 | ... | 查看 |
| xx项目 | xxx路1204号 | xx国际 | xx物业   | 2005-10-24 | ... | 查看 |

#### 项目详情

- 项目详情展示；
- 可以添加在职员工到项目参与人；
- 可以安排参与人的具体项目担任的角色；
- 项目的相关材料；
- 项目的当前状态；
- 可以安排项目的`工作流程`。

### 7、文档管理

#### 合同/证书电子归档等文档，方便检索

- 添加文档；
- 文档标签；
- 标签分组。

### 8、权限控制

企业管理员可在协会端权限分配范围内对企业内部的资源访问进行角色权限管理控制。


## 四、从业人员端

### 注册、登录

- 身份证上传OCR识别注册并登录；
- 登录临时密码，变更登录密码；
- 手机，邮箱绑定等常用功能。

### 文档管理

- 添加文档；
- 文档标签；
- 标签分组。

### 简历管理

- 编辑个人简历支持多版本；
- 可以设置 `公开`/`保密`模式；
- 简历可以选择关联个人的`资质`，`证书`。

### 资质、证书、职称、申报

#### 已经完成的

- 协会机构颁发的：资质、证书的原始文件；
- 也可以自行上传其他证书资质原始文件，学历证明等。

#### 可以申报的

| **资质/证书名称**          | **等级** | **核心要求**                                                                            | **适用范围**                           |
| -------------------------- | -------- | --------------------------------------------------------------------------------------- | -------------------------------------- |
| **注册建造师执业资格证书** | 二级     | - 建筑工程相关专业本科+3年经验<br>- 通过全国统一考试并注册<br>- 有效期3年（需继续教育） | 中小型工程项目经理、幕墙维修技术负责人 |

- 证书、职称等均可以安排指定对应的`工作流`；
- 点击名称查看申报流程和要求，以及对应的`课程`, `培训`等职位规划助力个人发展等内容；
- 当用户点击课程、培训等参加报名后可在协会端看到参与人。

#### 配合填报

由企业管理人员、负责人等发起的邀请填报内容，比如项目要求的信息填报等；

| 项目                                     | 截至时间            | 详情     |
| ---------------------------------------- | ------------------- | -------- |
| **上海市既有建筑幕墙维修管理人员申请表** | 2025-10-05 23:59:59 | [填报]() |
| 上海市既有建筑幕墙维修施工人员申请表     | 2025-11-05 23:59:59 | [填报]() |

- 点击`填报`弹出对应的模板表单；
- 当前用户即`从业人员`填写提交后可在企业端看到相应的填报内容。

### 从业人员加入的企业、项目

- 按照企业分组展示

| 企业             | 部门         | 职位         | 状态   | 详情 |
| ---------------- | ------------ | ------------ | ------ | ---- |
| xxx幕墙维修企业A | 幕墙维修管理 | 高级安全工程 | 在职中 | 查看 |
| xxx幕墙检查企业B | 幕墙检查管理 | 检查组组长   | 已离职 | 查看 |

- 点击详情查看打开详情页面

#### 参与的企业项目

- 展示相应参加的具体项目列表

| 名称   | 地址        | 产权人 | 物业单位 | 原竣工日期 | ... | 详情 |
| ------ | ----------- | ------ | -------- | ---------- | --- | ---- |
| xx项目 | xxx路1204号 | xx国际 | xx物业   | 2005-10-24 | ... | 查看 |
| xx项目 | xxx路1204号 | xx国际 | xx物业   | 2005-10-24 | ... | 查看 |


#### 参与的企业项目详情

- 查看当前`从业人员`在项目`角色`中对应的项目管理数据；
- 离职人员不能查看项目的详情数据。


## 五、消费者端

### 1、用户注册登录

- 手机号注册；
- 短信验证码登录；
- 绑定微信。

### 2、需求服务流程设计

**需求发布**：使用协会标准化需求模板发布需求，旨在方便消费者了解需求的布局和规划，快速的发布需求内容
以下是协会管理端配置的工作流示例，可以更具需要进行调整:

#### 1. 前期准备阶段

```mermaid
graph TD
  A[前期准备阶段] --> A1[需求梳理]
  A1 --> A1a(装修风格)
  A1 --> A1b(功能分区)
  A1 --> A1c(预算分配)
  A --> A2[装修模式选择]
  A2 --> A2a(全包)
  A2 --> A2b(半包)
  A2 --> A2c(清包)
  A --> A3[合同签订]
  A3 --> A3a(工期/付款)
  A3 --> A3b(材料清单)
  A3 --> A3c(违约责任)
  A -.-> |"Next →"| B[设计阶段]
```

#### 2. 设计阶段

```mermaid
graph TD
  B[设计阶段] --> B1[量房与平面布局]
  B1 --> B1a(测量尺寸)
  B1 --> B1b(动线优化)
  B --> B2[3D效果图确认]
  B2 --> B2a(材质核对)
  B2 --> B2b(颜色匹配)
  B --> B3[施工图深化]
  B3 --> B3a(水电定位)
  B3 --> B3b(插座预留)
  B -.-> |"Next →"| C[施工阶段]
```


#### 3. 施工阶段

```mermaid
graph TD
  C[施工阶段] --> C1[拆改与水电]
  C1 --> C1a(拆墙/砌墙)
  C1 --> C1b(水管走顶)
  C1 --> C1c(电路分路)
  C1 --> C1d{{⚠️隐蔽工程验收}}
  C --> C2[泥瓦与木工]
  C2 --> C2a(贴砖空鼓检查)
  C2 --> C2b(吊顶/柜体定制)
  C --> C3[油漆与主材安装]
  C3 --> C3a(墙面乳胶漆)
  C3 --> C3b(橱柜→地板→灯具)
  C -.-> |"Next →"| D[验收阶段]
```


#### 4. 验收收尾阶段

```mermaid
graph TD
  D[验收收尾阶段] --> D1[分项验收]
  D1 --> D1a(水电通路测试)
  D1 --> D1b(瓷砖/吊顶检查)
  D1 --> D1c(墙面平整度)
  D --> D2[空气质量检测]
  D2 --> D2a(甲醛/TVOC)
  D --> D3[软装与通风]
  D3 --> D3a(家具进场)
  D3 --> D3b(通风3-6个月)
```

### 3、我发布的需求列表

消费者发布的需求列表字段展示当时使用的模板的概要字段；

| 名称     | 当前状态 | 供应商  | 地点                 | 联系人 | 时间       | 详情     |
| -------- | -------- | ------- | -------------------- | ------ | ---------- | -------- |
| 家庭装修 | 开工中   | xxx公司 | 平凉路1234号56弄4453 | xx     | 2025-01-01 | [查看]() |
| 门店装修 | 开工中   | xxx公司 | 平凉路1234号56弄4453 | xx     | 2025-01-01 | [查看]() |

点开`详情`内容查看字段的详细内容，比如需求的完成进度和`工作流`状态，整体预算，描述，施工日志等记录；
配合工作流的阶段和对应的状态，展示具体的信息内容。


## 六、综合门户

### 1、综合门户说明

- 未登录账号展示页面；
- 已经登录账号可根据`用户`条件对页面进行适当调整。

### 2、统一登录入口

提供不同角色登录注册, 点击角色登录注册展示不同的操作页面；登录默认在门户站点，或者配置跳转到账号后台；

### 3、资源检索服务

各个`用户`端都可以在这里去查找对应的内容, 可以配置角色的展示页面情况，比如消费者端可以`找材料`、`找企业`而**不需要**去`找需求`；

#### 找需求

按用户的检索内容，进行匹配查找对应的需求内容，包括企业发布的需求和消费这发布的需求；方便企业拓展业务；

#### 找材料

按用户的检索内容，进行`装饰装修类材料`的匹配内容等；方便用户了解行业材料的具体情况；

#### 找企业

按用户的检索内容，进行企业的匹配查找， 方便用户了解企业的具体情况；

### 4、行业动态

1. 公示通报: 展示会议、处罚、奖励、中标等通告内容；放在最前面体现协会的权威；
2. 明星企业: 凸显名企会员的影响力名气；
3. 标杆项目: 展示优秀的成果；
4. 优秀专家: 展示从业人员的专业能力；
5. 学习、培训报名: 方便学员匹配参与课程。


## 七、技术服务方案

### 1、服务架构

```mermaid
graph TD
    A[前端] --> B[API网关]
    B --> C[会员服务]
    B --> D[资质服务]
    B --> E[项目服务]
    B --> X[...微服务]
    C --> F[主/从数据库、缓存数据库]
    D --> F[主/从数据库、缓存数据库]
    E --> F[主/从数据库、缓存数据库]
    X --> F[主/从数据库、缓存数据库]
```

### 2、实施计划

具体实施按照需求方要求；增项内容可另作评估，如资源消耗合理都可以纳入成果交付。

#### 阶段规划

| 阶段       | 周期    | 交付物                |
| ---------- | ------- | --------------------- |
| 需求确认   | 4-6 周  | 详细需求文档          |
| 核心开发   | 8-12 周 | 各个端基础功能        |
| 系统集成   | 4-6 周  | 完整测试环境+对接文档 |
| 试点运行   | 4-6 周  | 企业试点报告       |
| 正式上线   | 2-4 周  | 各端开放使用          |
| 上线后补丁 | 1周内   | 修复发版              |


#### 资源投入

- 开发团队：3人（含1名架构师）；
- 测试资源：2名测试人员；
- 硬件配置：私有化部署（4核8G×3节点）。

#### 运维保障

- 用户量监控；
- 服务健康检查；
- 服务用量监控，配额告警；
- 服务器技术指标实时告警；
- 客户要求的实时告警。

#### 数据安全

- 敏感字段加密：身份证、手机号、邮箱、银行账号等；
- 加密传输（SSL/TLS）、存储加密、脱敏处理；
- 操作审计：保留180天完整日志。

#### 服务承诺

- 系统可用性：99.5%（工作日8:00-20:00）；
- 故障响应：8小时内在线支持，节假日24小时内响应。


### 3、演示网址

https://xxxx.xxxx

