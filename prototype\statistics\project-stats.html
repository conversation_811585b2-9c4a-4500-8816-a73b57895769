<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目统计分析 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Microsoft YaHei', sans-serif; }
        .navbar { background: linear-gradient(45deg, #667eea, #764ba2); box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .sidebar { background: white; min-height: calc(100vh - 76px); box-shadow: 2px 0 10px rgba(0,0,0,0.1); padding: 0; }
        .sidebar-menu { list-style: none; padding: 1rem 0; margin: 0; }
        .sidebar-menu li { margin-bottom: 0.5rem; }
        .sidebar-menu a { display: block; padding: 0.8rem 1.5rem; color: #333; text-decoration: none; transition: all 0.3s ease; border-left: 3px solid transparent; }
        .sidebar-menu a:hover, .sidebar-menu a.active { background: rgba(102, 126, 234, 0.1); border-left-color: #667eea; color: #667eea; }
        .sidebar-menu i { width: 20px; margin-right: 10px; }
        .main-content { padding: 2rem; }
        .page-header { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .stats-card { background: white; border-radius: 15px; padding: 1.5rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); text-align: center; transition: transform 0.3s ease; margin-bottom: 2rem; }
        .stats-card:hover { transform: translateY(-5px); }
        .stats-icon { width: 60px; height: 60px; border-radius: 15px; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; color: white; margin: 0 auto 1rem; }
        .stats-number { font-size: 2rem; font-weight: 700; margin-bottom: 0.5rem; }
        .stats-label { color: #666; font-size: 0.9rem; }
        .chart-card { background: white; border-radius: 15px; padding: 1.5rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); margin-bottom: 2rem; }
        .chart-title { font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem; color: #333; }
        .filter-card { background: white; border-radius: 15px; padding: 1.5rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); margin-bottom: 2rem; }
        .btn-primary { background: linear-gradient(45deg, #667eea, #764ba2); border: none; border-radius: 8px; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3); }
        .progress-ring { width: 120px; height: 120px; margin: 0 auto; }
        .progress-ring circle { fill: transparent; stroke-width: 8; }
        .progress-ring .bg { stroke: #e9ecef; }
        .progress-ring .progress { stroke: #667eea; stroke-linecap: round; transition: stroke-dasharray 0.5s ease; }
        .progress-text { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 1.2rem; font-weight: 600; }
        .timeline-item { position: relative; padding-left: 2rem; margin-bottom: 1.5rem; }
        .timeline-item::before { content: ''; position: absolute; left: 0.5rem; top: 0.5rem; width: 1rem; height: 1rem; border-radius: 50%; background: #667eea; }
        .timeline-item.completed::before { background: #28a745; }
        .timeline-item.delayed::before { background: #dc3545; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.html"><i class="fas fa-building"></i> 幕墙信息化平台</a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="../company/list.html"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="../project/list.html"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="../curtain-wall/list.html"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="overview.html" class="active"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="../system/users.html"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb mb-2">
                                        <li class="breadcrumb-item"><a href="overview.html">统计分析</a></li>
                                        <li class="breadcrumb-item active">项目统计</li>
                                    </ol>
                                </nav>
                                <h2><i class="fas fa-project-diagram"></i> 项目统计分析</h2>
                                <p class="text-muted mb-0">项目进度、投资规模、完成情况等多维度统计分析</p>
                            </div>
                            <div>
                                <button class="btn btn-outline-primary me-2" onclick="window.location.href='company-stats.html'">
                                    <i class="fas fa-industry"></i> 企业统计
                                </button>
                                <button class="btn btn-primary" onclick="exportReport()">
                                    <i class="fas fa-download"></i> 导出报表
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选条件 -->
                    <div class="filter-card">
                        <h5><i class="fas fa-filter"></i> 筛选条件</h5>
                        <form class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">项目状态</label>
                                <select class="form-select">
                                    <option value="">全部状态</option>
                                    <option value="1">规划中</option>
                                    <option value="2">施工中</option>
                                    <option value="3">已完工</option>
                                    <option value="4">已验收</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">幕墙类型</label>
                                <select class="form-select">
                                    <option value="">全部类型</option>
                                    <option value="glass">玻璃幕墙</option>
                                    <option value="stone">石材幕墙</option>
                                    <option value="metal">金属幕墙</option>
                                    <option value="composite">组合幕墙</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">项目地区</label>
                                <select class="form-select">
                                    <option value="">全部地区</option>
                                    <option value="pudong">浦东新区</option>
                                    <option value="huangpu">黄浦区</option>
                                    <option value="jingan">静安区</option>
                                    <option value="xuhui">徐汇区</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 筛选
                                    </button>
                                    <button type="reset" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo"></i> 重置
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 核心指标卡片 -->
                    <div class="row">
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="stats-icon" style="background: linear-gradient(45deg, #667eea, #764ba2);">
                                    <i class="fas fa-project-diagram"></i>
                                </div>
                                <div class="stats-number text-primary">89</div>
                                <div class="stats-label">项目总数</div>
                                <small class="text-success"><i class="fas fa-arrow-up"></i> 较上月增长 12.3%</small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="stats-icon" style="background: linear-gradient(45deg, #ffc107, #fd7e14);">
                                    <i class="fas fa-hammer"></i>
                                </div>
                                <div class="stats-number text-warning">45</div>
                                <div class="stats-label">施工中项目</div>
                                <small class="text-info"><i class="fas fa-arrow-up"></i> 较上月增长 8.1%</small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="stats-icon" style="background: linear-gradient(45deg, #28a745, #20c997);">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stats-number text-success">32</div>
                                <div class="stats-label">已完工项目</div>
                                <small class="text-success"><i class="fas fa-arrow-up"></i> 较上月增长 15.2%</small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="stats-icon" style="background: linear-gradient(45deg, #dc3545, #e83e8c);">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="stats-number text-danger">28.5亿</div>
                                <div class="stats-label">总投资额</div>
                                <small class="text-success"><i class="fas fa-arrow-up"></i> 较去年增长 18.7%</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- 项目状态分布 -->
                        <div class="col-lg-6">
                            <div class="chart-card">
                                <h5 class="chart-title">
                                    <i class="fas fa-chart-pie"></i> 项目状态分布
                                </h5>
                                <div style="height: 300px;">
                                    <canvas id="projectStatusChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- 项目进度概览 -->
                        <div class="col-lg-6">
                            <div class="chart-card">
                                <h5 class="chart-title">
                                    <i class="fas fa-tasks"></i> 项目进度概览
                                </h5>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="position-relative d-inline-block">
                                            <svg class="progress-ring" viewBox="0 0 120 120">
                                                <circle class="bg" cx="60" cy="60" r="54"></circle>
                                                <circle class="progress" cx="60" cy="60" r="54"
                                                        stroke-dasharray="339.29" stroke-dashoffset="101.79"></circle>
                                            </svg>
                                            <div class="progress-text">70%</div>
                                        </div>
                                        <div class="mt-2">
                                            <div class="fw-bold">平均进度</div>
                                            <small class="text-muted">所有项目</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="position-relative d-inline-block">
                                            <svg class="progress-ring" viewBox="0 0 120 120">
                                                <circle class="bg" cx="60" cy="60" r="54"></circle>
                                                <circle class="progress" cx="60" cy="60" r="54"
                                                        stroke-dasharray="339.29" stroke-dashoffset="67.86" stroke="#28a745"></circle>
                                            </svg>
                                            <div class="progress-text">80%</div>
                                        </div>
                                        <div class="mt-2">
                                            <div class="fw-bold">按时完成率</div>
                                            <small class="text-muted">进度正常</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="position-relative d-inline-block">
                                            <svg class="progress-ring" viewBox="0 0 120 120">
                                                <circle class="bg" cx="60" cy="60" r="54"></circle>
                                                <circle class="progress" cx="60" cy="60" r="54"
                                                        stroke-dasharray="339.29" stroke-dashoffset="271.43" stroke="#ffc107"></circle>
                                            </svg>
                                            <div class="progress-text">20%</div>
                                        </div>
                                        <div class="mt-2">
                                            <div class="fw-bold">延期项目</div>
                                            <small class="text-muted">需要关注</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- 投资规模分布 -->
                        <div class="col-lg-8">
                            <div class="chart-card">
                                <h5 class="chart-title">
                                    <i class="fas fa-chart-bar"></i> 投资规模分布
                                </h5>
                                <div style="height: 300px;">
                                    <canvas id="investmentChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- 项目时间线 -->
                        <div class="col-lg-4">
                            <div class="chart-card">
                                <h5 class="chart-title">
                                    <i class="fas fa-clock"></i> 近期项目动态
                                </h5>
                                <div class="timeline-item completed">
                                    <div class="fw-bold">上海某写字楼项目</div>
                                    <small class="text-muted">2024-02-15 完工验收</small>
                                </div>
                                <div class="timeline-item">
                                    <div class="fw-bold">上海某商业大厦</div>
                                    <small class="text-muted">2024-02-10 进度更新</small>
                                </div>
                                <div class="timeline-item">
                                    <div class="fw-bold">上海某酒店项目</div>
                                    <small class="text-muted">2024-02-08 开工建设</small>
                                </div>
                                <div class="timeline-item delayed">
                                    <div class="fw-bold">上海某办公楼</div>
                                    <small class="text-muted">2024-02-05 进度延期</small>
                                </div>
                                <div class="timeline-item completed">
                                    <div class="fw-bold">上海某购物中心</div>
                                    <small class="text-muted">2024-02-01 质量检查</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- 月度完成情况 -->
                        <div class="col-lg-8">
                            <div class="chart-card">
                                <h5 class="chart-title">
                                    <i class="fas fa-chart-line"></i> 月度项目完成情况
                                </h5>
                                <div style="height: 300px;">
                                    <canvas id="monthlyCompletionChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- 幕墙类型分布 -->
                        <div class="col-lg-4">
                            <div class="chart-card">
                                <h5 class="chart-title">
                                    <i class="fas fa-building"></i> 幕墙类型分布
                                </h5>
                                <div style="height: 300px;">
                                    <canvas id="curtainWallTypeChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 项目状态分布图表
        const statusCtx = document.getElementById('projectStatusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['规划中', '施工中', '已完工', '已验收'],
                datasets: [{
                    data: [12, 45, 32, 12],
                    backgroundColor: ['#6c757d', '#ffc107', '#28a745', '#667eea']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { position: 'bottom' } }
            }
        });

        // 投资规模分布图表
        const investmentCtx = document.getElementById('investmentChart').getContext('2d');
        new Chart(investmentCtx, {
            type: 'bar',
            data: {
                labels: ['1000万以下', '1000-5000万', '5000万-1亿', '1亿-5亿', '5亿以上'],
                datasets: [{
                    label: '项目数量',
                    data: [25, 35, 18, 8, 3],
                    backgroundColor: '#667eea'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } }
            }
        });

        // 月度完成情况图表
        const monthlyCtx = document.getElementById('monthlyCompletionChart').getContext('2d');
        new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: ['2023-09', '2023-10', '2023-11', '2023-12', '2024-01', '2024-02'],
                datasets: [{
                    label: '新开工项目',
                    data: [5, 8, 12, 6, 10, 15],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                }, {
                    label: '完工项目',
                    data: [3, 5, 8, 10, 7, 12],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { position: 'bottom' } }
            }
        });

        // 幕墙类型分布图表
        const typeCtx = document.getElementById('curtainWallTypeChart').getContext('2d');
        new Chart(typeCtx, {
            type: 'pie',
            data: {
                labels: ['玻璃幕墙', '石材幕墙', '金属幕墙', '组合幕墙'],
                datasets: [{
                    data: [42, 28, 18, 12],
                    backgroundColor: ['#667eea', '#28a745', '#ffc107', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { position: 'bottom' } }
            }
        });

        // 导出报表
        function exportReport() {
            alert('正在生成项目统计报表...');
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stats-card, .chart-card, .filter-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
