<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
            padding: 2rem 0;
        }
        .register-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            margin: 0 auto;
            overflow: hidden;
        }
        .register-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .register-header h2 {
            margin-bottom: 0.5rem;
            font-weight: 300;
        }
        .register-body {
            padding: 3rem;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 3rem;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 0.5rem;
            transition: all 0.3s ease;
        }
        .step.active .step-number {
            background: #667eea;
            color: white;
        }
        .step.completed .step-number {
            background: #28a745;
            color: white;
        }
        .step-line {
            width: 60px;
            height: 2px;
            background: #e9ecef;
            margin: 0 1rem;
        }
        .step.completed + .step-line {
            background: #28a745;
        }
        .form-step {
            display: none;
        }
        .form-step.active {
            display: block;
        }
        .form-floating {
            margin-bottom: 1.5rem;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-next, .btn-prev, .btn-submit {
            border-radius: 10px;
            padding: 0.8rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-next, .btn-submit {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
        }
        .btn-next:hover, .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        .company-type-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        .company-type-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .company-type-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
        }
        .company-type-card.selected {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }
        .company-type-card i {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        .file-upload-area {
            border: 2px dashed #e9ecef;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }
        .file-upload-area:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }
        .file-upload-area.dragover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }
        .uploaded-files {
            margin-top: 1rem;
        }
        .uploaded-file {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 5px;
            margin-bottom: 0.5rem;
        }
        .back-login {
            position: absolute;
            top: 2rem;
            left: 2rem;
            color: white;
            font-size: 1.2rem;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .back-login:hover {
            color: rgba(255, 255, 255, 0.8);
            transform: translateX(-5px);
        }
        .agreement-text {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 1rem;
            background: #f8f9fa;
            font-size: 0.9rem;
            line-height: 1.6;
        }
        @media (max-width: 768px) {
            .register-body {
                padding: 2rem;
            }
            .step-indicator {
                flex-wrap: wrap;
            }
            .step-line {
                display: none;
            }
        }
    </style>
</head>
<body>
    <a href="login.html" class="back-login">
        <i class="fas fa-arrow-left"></i> 返回登录
    </a>

    <div class="container">
        <div class="register-container">
            <div class="register-header">
                <h2><i class="fas fa-user-plus"></i> 用户注册</h2>
                <p>加入幕墙信息化平台，享受专业服务</p>
            </div>
            
            <div class="register-body">
                <!-- 步骤指示器 -->
                <div class="step-indicator">
                    <div class="step active" id="step1">
                        <div class="step-number">1</div>
                        <span>选择类型</span>
                    </div>
                    <div class="step-line"></div>
                    <div class="step" id="step2">
                        <div class="step-number">2</div>
                        <span>基本信息</span>
                    </div>
                    <div class="step-line"></div>
                    <div class="step" id="step3">
                        <div class="step-number">3</div>
                        <span>企业信息</span>
                    </div>
                    <div class="step-line"></div>
                    <div class="step" id="step4">
                        <div class="step-number">4</div>
                        <span>完成注册</span>
                    </div>
                </div>

                <form id="registerForm">
                    <!-- 第一步：选择企业类型 -->
                    <div class="form-step active" id="formStep1">
                        <h4 class="mb-4 text-center">请选择您的企业类型</h4>
                        <div class="company-type-cards">
                            <div class="company-type-card" data-type="1">
                                <i class="fas fa-cube"></i>
                                <h5>幕墙材料企业</h5>
                                <p class="text-muted">专业从事幕墙材料生产和销售</p>
                            </div>
                            <div class="company-type-card" data-type="2">
                                <i class="fas fa-hammer"></i>
                                <h5>幕墙施工企业</h5>
                                <p class="text-muted">专业从事幕墙工程施工</p>
                            </div>
                            <div class="company-type-card" data-type="3">
                                <i class="fas fa-tools"></i>
                                <h5>既有幕墙维修企业</h5>
                                <p class="text-muted">专业从事既有幕墙维修服务</p>
                            </div>
                            <div class="company-type-card" data-type="4">
                                <i class="fas fa-search"></i>
                                <h5>既有幕墙检查服务企业</h5>
                                <p class="text-muted">专业从事既有幕墙检查评估</p>
                            </div>
                        </div>
                        <input type="hidden" id="companyType" name="companyType" required>
                        <div class="text-center">
                            <button type="button" class="btn btn-primary btn-next" onclick="nextStep()" disabled>
                                下一步 <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 第二步：基本信息 -->
                    <div class="form-step" id="formStep2">
                        <h4 class="mb-4 text-center">填写基本信息</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="username" name="username" placeholder="用户名" required>
                                    <label for="username">用户名</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="realName" name="realName" placeholder="真实姓名" required>
                                    <label for="realName">真实姓名</label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="email" class="form-control" id="email" name="email" placeholder="邮箱" required>
                                    <label for="email">邮箱地址</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="tel" class="form-control" id="phone" name="phone" placeholder="手机号" required>
                                    <label for="phone">手机号码</label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                                    <label for="password">登录密码</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" placeholder="确认密码" required>
                                    <label for="confirmPassword">确认密码</label>
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="button" class="btn btn-secondary btn-prev me-2" onclick="prevStep()">
                                <i class="fas fa-arrow-left"></i> 上一步
                            </button>
                            <button type="button" class="btn btn-primary btn-next" onclick="nextStep()">
                                下一步 <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 第三步：企业信息 -->
                    <div class="form-step" id="formStep3">
                        <h4 class="mb-4 text-center">填写企业信息</h4>
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="companyName" name="companyName" placeholder="企业名称" required>
                            <label for="companyName">企业名称</label>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="legalPerson" name="legalPerson" placeholder="法定代表人" required>
                                    <label for="legalPerson">法定代表人</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="businessLicense" name="businessLicense" placeholder="营业执照号" required>
                                    <label for="businessLicense">统一社会信用代码</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="address" name="address" placeholder="企业地址" required>
                            <label for="address">企业地址</label>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="contactPerson" name="contactPerson" placeholder="联系人" required>
                                    <label for="contactPerson">联系人</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="tel" class="form-control" id="contactPhone" name="contactPhone" placeholder="联系电话" required>
                                    <label for="contactPhone">联系电话</label>
                                </div>
                            </div>
                        </div>
                        
                        <h5 class="mt-4 mb-3">上传企业资质文件</h5>
                        <div class="file-upload-area" onclick="document.getElementById('fileInput').click()">
                            <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                            <p class="text-muted">点击或拖拽文件到此处上传</p>
                            <small class="text-muted">支持 PDF、JPG、PNG 格式，单个文件不超过 10MB</small>
                        </div>
                        <input type="file" id="fileInput" multiple accept=".pdf,.jpg,.jpeg,.png" style="display: none;">
                        <div class="uploaded-files" id="uploadedFiles"></div>
                        
                        <div class="text-center">
                            <button type="button" class="btn btn-secondary btn-prev me-2" onclick="prevStep()">
                                <i class="fas fa-arrow-left"></i> 上一步
                            </button>
                            <button type="button" class="btn btn-primary btn-next" onclick="nextStep()">
                                下一步 <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 第四步：完成注册 -->
                    <div class="form-step" id="formStep4">
                        <h4 class="mb-4 text-center">确认信息并完成注册</h4>
                        
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-info-circle"></i> 注册信息确认</h5>
                            </div>
                            <div class="card-body" id="confirmInfo">
                                <!-- 确认信息将通过JavaScript动态生成 -->
                            </div>
                        </div>
                        
                        <div class="agreement-text mb-3">
                            <h6>用户协议和隐私政策</h6>
                            <p>1. 用户在使用本平台服务时，应遵守国家相关法律法规。</p>
                            <p>2. 用户提供的信息应真实、准确、完整，如有虚假信息，平台有权拒绝提供服务。</p>
                            <p>3. 平台将严格保护用户隐私，不会向第三方泄露用户信息。</p>
                            <p>4. 用户应妥善保管账号密码，如因用户原因导致账号被盗用，平台不承担责任。</p>
                            <p>5. 平台有权根据业务需要调整服务内容，并提前通知用户。</p>
                            <p>6. 如有争议，双方应友好协商解决，协商不成的，提交有管辖权的人民法院解决。</p>
                        </div>
                        
                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                            <label class="form-check-label" for="agreeTerms">
                                我已阅读并同意 <a href="#" class="text-primary">用户协议</a> 和 <a href="#" class="text-primary">隐私政策</a>
                            </label>
                        </div>
                        
                        <div class="text-center">
                            <button type="button" class="btn btn-secondary btn-prev me-2" onclick="prevStep()">
                                <i class="fas fa-arrow-left"></i> 上一步
                            </button>
                            <button type="submit" class="btn btn-success btn-submit">
                                <i class="fas fa-check"></i> 完成注册
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentStep = 1;
        const totalSteps = 4;
        let selectedCompanyType = null;

        // 企业类型选择
        document.querySelectorAll('.company-type-card').forEach(card => {
            card.addEventListener('click', function() {
                document.querySelectorAll('.company-type-card').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
                selectedCompanyType = this.dataset.type;
                document.getElementById('companyType').value = selectedCompanyType;
                document.querySelector('.btn-next').disabled = false;
            });
        });

        // 下一步
        function nextStep() {
            if (validateCurrentStep()) {
                if (currentStep < totalSteps) {
                    // 隐藏当前步骤
                    document.getElementById(`formStep${currentStep}`).classList.remove('active');
                    document.getElementById(`step${currentStep}`).classList.remove('active');
                    document.getElementById(`step${currentStep}`).classList.add('completed');
                    
                    // 显示下一步
                    currentStep++;
                    document.getElementById(`formStep${currentStep}`).classList.add('active');
                    document.getElementById(`step${currentStep}`).classList.add('active');
                    
                    // 如果是最后一步，生成确认信息
                    if (currentStep === 4) {
                        generateConfirmInfo();
                    }
                }
            }
        }

        // 上一步
        function prevStep() {
            if (currentStep > 1) {
                // 隐藏当前步骤
                document.getElementById(`formStep${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.remove('active');
                
                // 显示上一步
                currentStep--;
                document.getElementById(`formStep${currentStep}`).classList.add('active');
                document.getElementById(`step${currentStep}`).classList.add('active');
                document.getElementById(`step${currentStep}`).classList.remove('completed');
            }
        }

        // 验证当前步骤
        function validateCurrentStep() {
            switch (currentStep) {
                case 1:
                    if (!selectedCompanyType) {
                        alert('请选择企业类型！');
                        return false;
                    }
                    break;
                case 2:
                    const requiredFields2 = ['username', 'realName', 'email', 'phone', 'password', 'confirmPassword'];
                    for (let field of requiredFields2) {
                        if (!document.getElementById(field).value.trim()) {
                            alert('请填写完整的基本信息！');
                            return false;
                        }
                    }
                    
                    // 验证密码一致性
                    const password = document.getElementById('password').value;
                    const confirmPassword = document.getElementById('confirmPassword').value;
                    if (password !== confirmPassword) {
                        alert('两次输入的密码不一致！');
                        return false;
                    }
                    
                    // 验证邮箱格式
                    const email = document.getElementById('email').value;
                    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                        alert('请输入正确的邮箱地址！');
                        return false;
                    }
                    
                    // 验证手机号格式
                    const phone = document.getElementById('phone').value;
                    if (!/^1[3-9]\d{9}$/.test(phone)) {
                        alert('请输入正确的手机号码！');
                        return false;
                    }
                    break;
                case 3:
                    const requiredFields3 = ['companyName', 'legalPerson', 'businessLicense', 'address', 'contactPerson', 'contactPhone'];
                    for (let field of requiredFields3) {
                        if (!document.getElementById(field).value.trim()) {
                            alert('请填写完整的企业信息！');
                            return false;
                        }
                    }
                    break;
                case 4:
                    if (!document.getElementById('agreeTerms').checked) {
                        alert('请阅读并同意用户协议和隐私政策！');
                        return false;
                    }
                    break;
            }
            return true;
        }

        // 生成确认信息
        function generateConfirmInfo() {
            const companyTypes = {
                '1': '幕墙材料企业',
                '2': '幕墙施工企业',
                '3': '既有幕墙维修企业',
                '4': '既有幕墙检查服务企业'
            };
            
            const confirmInfo = `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>企业类型：</strong>${companyTypes[selectedCompanyType]}</p>
                        <p><strong>用户名：</strong>${document.getElementById('username').value}</p>
                        <p><strong>真实姓名：</strong>${document.getElementById('realName').value}</p>
                        <p><strong>邮箱：</strong>${document.getElementById('email').value}</p>
                        <p><strong>手机号：</strong>${document.getElementById('phone').value}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>企业名称：</strong>${document.getElementById('companyName').value}</p>
                        <p><strong>法定代表人：</strong>${document.getElementById('legalPerson').value}</p>
                        <p><strong>统一社会信用代码：</strong>${document.getElementById('businessLicense').value}</p>
                        <p><strong>企业地址：</strong>${document.getElementById('address').value}</p>
                        <p><strong>联系人：</strong>${document.getElementById('contactPerson').value}</p>
                    </div>
                </div>
            `;
            
            document.getElementById('confirmInfo').innerHTML = confirmInfo;
        }

        // 文件上传处理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            handleFiles(e.target.files);
        });

        // 拖拽上传
        const uploadArea = document.querySelector('.file-upload-area');
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });

        function handleFiles(files) {
            const uploadedFiles = document.getElementById('uploadedFiles');
            
            Array.from(files).forEach(file => {
                if (file.size > 10 * 1024 * 1024) {
                    alert(`文件 ${file.name} 超过10MB限制！`);
                    return;
                }
                
                const fileDiv = document.createElement('div');
                fileDiv.className = 'uploaded-file';
                fileDiv.innerHTML = `
                    <span><i class="fas fa-file"></i> ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)</span>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                uploadedFiles.appendChild(fileDiv);
            });
        }

        // 表单提交
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (validateCurrentStep()) {
                // 模拟注册过程
                const submitBtn = document.querySelector('.btn-submit');
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 注册中...';
                submitBtn.disabled = true;
                
                setTimeout(() => {
                    alert('注册申请提交成功！\n\n您的申请已提交给协会管理员审核，审核结果将通过短信和邮件通知您。\n\n预计审核时间：1-3个工作日');
                    window.location.href = 'login.html';
                }, 2000);
            }
        });

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.register-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.6s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
