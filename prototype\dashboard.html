<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统首页 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar {
            background: linear-gradient(45deg, #667eea, #764ba2);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            font-weight: 600;
            font-size: 1.3rem;
        }
        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 0;
        }
        .sidebar-menu {
            list-style: none;
            padding: 1rem 0;
            margin: 0;
        }
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        .sidebar-menu a {
            display: block;
            padding: 0.8rem 1.5rem;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background: rgba(102, 126, 234, 0.1);
            border-left-color: #667eea;
            color: #667eea;
        }
        .sidebar-menu i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            padding: 2rem;
        }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: none;
            margin-bottom: 1.5rem;
        }
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }
        .stats-label {
            color: #666;
            font-size: 0.9rem;
        }
        .chart-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 1.5rem;
        }
        .chart-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }
        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 1.5rem;
        }
        .action-btn {
            display: block;
            width: 100%;
            padding: 1rem;
            margin-bottom: 0.8rem;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
        }
        .action-btn:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
            color: #667eea;
            transform: translateX(5px);
        }
        .action-btn i {
            margin-right: 10px;
            width: 20px;
        }
        .recent-activities {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .activity-item {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .activity-item:last-child {
            border-bottom: none;
        }
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 0.9rem;
        }
        .activity-content {
            flex: 1;
        }
        .activity-title {
            font-weight: 600;
            margin-bottom: 0.2rem;
        }
        .activity-time {
            font-size: 0.8rem;
            color: #666;
        }
        .welcome-banner {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }
        .welcome-banner::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 200px;
            height: 200px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
        }
        .welcome-title {
            font-size: 1.5rem;
            font-weight: 300;
            margin-bottom: 0.5rem;
        }
        .welcome-subtitle {
            opacity: 0.9;
            margin-bottom: 0;
        }
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -250px;
                width: 250px;
                z-index: 1000;
                transition: left 0.3s ease;
            }
            .sidebar.show {
                left: 0;
            }
            .main-content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <button class="navbar-toggler d-lg-none" type="button" onclick="toggleSidebar()">
                <span class="navbar-toggler-icon"></span>
            </button>
            <a class="navbar-brand" href="#">
                <i class="fas fa-building"></i> 幕墙信息化平台
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell position-relative">
                            <span class="notification-badge">3</span>
                        </i>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-info-circle text-info"></i> 新企业注册申请</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-exclamation-triangle text-warning"></i> 资质即将到期提醒</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-check-circle text-success"></i> 项目验收通过</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#">查看全部通知</a></li>
                    </ul>
                </div>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user"></i> 个人资料</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-cog"></i> 系统设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar" id="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="dashboard.html" class="active"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="company/list.html"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="project/list.html"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="curtain-wall/list.html"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="statistics/overview.html"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="system/users.html"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 欢迎横幅 -->
                    <div class="welcome-banner">
                        <h2 class="welcome-title">欢迎回来，管理员</h2>
                        <p class="welcome-subtitle">今天是 <span id="currentDate"></span>，祝您工作愉快！</p>
                    </div>

                    <!-- 统计卡片 -->
                    <div class="row">
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="stats-icon" style="background: linear-gradient(45deg, #667eea, #764ba2);">
                                    <i class="fas fa-industry"></i>
                                </div>
                                <div class="stats-number">156</div>
                                <div class="stats-label">注册企业总数</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="stats-icon" style="background: linear-gradient(45deg, #28a745, #20c997);">
                                    <i class="fas fa-project-diagram"></i>
                                </div>
                                <div class="stats-number">89</div>
                                <div class="stats-label">在建项目数量</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="stats-icon" style="background: linear-gradient(45deg, #ffc107, #fd7e14);">
                                    <i class="fas fa-building-columns"></i>
                                </div>
                                <div class="stats-number">234</div>
                                <div class="stats-label">既有幕墙建筑</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="stats-icon" style="background: linear-gradient(45deg, #dc3545, #e83e8c);">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="stats-number">12</div>
                                <div class="stats-label">安全隐患预警</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- 图表区域 -->
                        <div class="col-lg-8">
                            <div class="chart-card">
                                <h5 class="chart-title">
                                    <i class="fas fa-chart-line"></i> 企业注册趋势
                                </h5>
                                <canvas id="registrationChart" height="300"></canvas>
                            </div>
                            
                            <div class="chart-card">
                                <h5 class="chart-title">
                                    <i class="fas fa-chart-pie"></i> 企业类型分布
                                </h5>
                                <canvas id="companyTypeChart" height="300"></canvas>
                            </div>
                        </div>

                        <!-- 右侧功能区 -->
                        <div class="col-lg-4">
                            <!-- 快捷操作 -->
                            <div class="quick-actions">
                                <h5 class="chart-title">
                                    <i class="fas fa-bolt"></i> 快捷操作
                                </h5>
                                <a href="company/edit.html" class="action-btn">
                                    <i class="fas fa-plus"></i> 新增企业
                                </a>
                                <a href="project/edit.html" class="action-btn">
                                    <i class="fas fa-plus"></i> 新增项目
                                </a>
                                <a href="curtain-wall/inspection.html" class="action-btn">
                                    <i class="fas fa-search"></i> 安全检查
                                </a>
                                <a href="statistics/reports.html" class="action-btn">
                                    <i class="fas fa-file-alt"></i> 生成报表
                                </a>
                                <a href="system/settings.html" class="action-btn">
                                    <i class="fas fa-cog"></i> 系统设置
                                </a>
                            </div>

                            <!-- 最近活动 -->
                            <div class="recent-activities">
                                <h5 class="chart-title">
                                    <i class="fas fa-history"></i> 最近活动
                                </h5>
                                <div class="activity-item">
                                    <div class="activity-icon" style="background: rgba(40, 167, 69, 0.1); color: #28a745;">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title">上海某幕墙公司注册审核通过</div>
                                        <div class="activity-time">2小时前</div>
                                    </div>
                                </div>
                                <div class="activity-item">
                                    <div class="activity-icon" style="background: rgba(255, 193, 7, 0.1); color: #ffc107;">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title">某大厦幕墙安全检查预警</div>
                                        <div class="activity-time">4小时前</div>
                                    </div>
                                </div>
                                <div class="activity-item">
                                    <div class="activity-icon" style="background: rgba(102, 126, 234, 0.1); color: #667eea;">
                                        <i class="fas fa-upload"></i>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title">月度统计报表已生成</div>
                                        <div class="activity-time">1天前</div>
                                    </div>
                                </div>
                                <div class="activity-item">
                                    <div class="activity-icon" style="background: rgba(220, 53, 69, 0.1); color: #dc3545;">
                                        <i class="fas fa-times"></i>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title">某企业资质审核未通过</div>
                                        <div class="activity-time">2天前</div>
                                    </div>
                                </div>
                                <div class="activity-item">
                                    <div class="activity-icon" style="background: rgba(23, 162, 184, 0.1); color: #17a2b8;">
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title">项目进度信息更新</div>
                                        <div class="activity-time">3天前</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 设置当前日期
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });

        // 切换侧边栏（移动端）
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('show');
        }

        // 企业注册趋势图表
        const registrationCtx = document.getElementById('registrationChart').getContext('2d');
        new Chart(registrationCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                datasets: [{
                    label: '新注册企业数',
                    data: [12, 15, 18, 22, 25, 28, 32, 35, 38, 42, 45, 48],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });

        // 企业类型分布图表
        const companyTypeCtx = document.getElementById('companyTypeChart').getContext('2d');
        new Chart(companyTypeCtx, {
            type: 'doughnut',
            data: {
                labels: ['幕墙材料企业', '幕墙施工企业', '既有幕墙维修企业', '既有幕墙检查服务企业'],
                datasets: [{
                    data: [45, 52, 32, 27],
                    backgroundColor: [
                        '#667eea',
                        '#28a745',
                        '#ffc107',
                        '#dc3545'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stats-card, .chart-card, .quick-actions, .recent-activities');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // 实时更新统计数据（模拟）
        setInterval(() => {
            const statsNumbers = document.querySelectorAll('.stats-number');
            statsNumbers.forEach(number => {
                const currentValue = parseInt(number.textContent);
                if (Math.random() > 0.8) { // 20%概率更新
                    const change = Math.random() > 0.5 ? 1 : -1;
                    number.textContent = Math.max(0, currentValue + change);
                }
            });
        }, 30000); // 每30秒更新一次
    </script>
</body>
</html>
