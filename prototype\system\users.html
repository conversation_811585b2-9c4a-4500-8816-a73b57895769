<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Microsoft YaHei', sans-serif; }
        .navbar { background: linear-gradient(45deg, #667eea, #764ba2); box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .sidebar { background: white; min-height: calc(100vh - 76px); box-shadow: 2px 0 10px rgba(0,0,0,0.1); padding: 0; }
        .sidebar-menu { list-style: none; padding: 1rem 0; margin: 0; }
        .sidebar-menu li { margin-bottom: 0.5rem; }
        .sidebar-menu a { display: block; padding: 0.8rem 1.5rem; color: #333; text-decoration: none; transition: all 0.3s ease; border-left: 3px solid transparent; }
        .sidebar-menu a:hover, .sidebar-menu a.active { background: rgba(102, 126, 234, 0.1); border-left-color: #667eea; color: #667eea; }
        .sidebar-menu i { width: 20px; margin-right: 10px; }
        .main-content { padding: 2rem; }
        .page-header { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .search-filters { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .user-table { background: white; border-radius: 15px; padding: 1.5rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .btn-primary { background: linear-gradient(45deg, #667eea, #764ba2); border: none; border-radius: 8px; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3); }
        .user-status-badge { padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.8rem; font-weight: 500; }
        .status-active { background: rgba(40, 167, 69, 0.1); color: #28a745; }
        .status-inactive { background: rgba(108, 117, 125, 0.1); color: #6c757d; }
        .role-badge { padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.8rem; font-weight: 500; }
        .role-admin { background: rgba(220, 53, 69, 0.1); color: #dc3545; }
        .role-company { background: rgba(102, 126, 234, 0.1); color: #667eea; }
        .role-inspector { background: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .table th { border-top: none; font-weight: 600; color: #333; background: #f8f9fa; }
        .action-buttons .btn { margin-right: 0.5rem; margin-bottom: 0.5rem; }
        .stats-cards { margin-bottom: 2rem; }
        .stats-card { background: white; border-radius: 15px; padding: 1.5rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); text-align: center; transition: transform 0.3s ease; }
        .stats-card:hover { transform: translateY(-5px); }
        .stats-number { font-size: 2rem; font-weight: 700; margin-bottom: 0.5rem; }
        .stats-label { color: #666; font-size: 0.9rem; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.html"><i class="fas fa-building"></i> 幕墙信息化平台</a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="../company/list.html"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="../project/list.html"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="../curtain-wall/list.html"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="../statistics/overview.html"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="users.html" class="active"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2><i class="fas fa-users"></i> 用户管理</h2>
                                <p class="text-muted mb-0">管理系统用户账号和权限</p>
                            </div>
                            <div>
                                <button class="btn btn-outline-primary me-2" onclick="window.location.href='roles.html'">
                                    <i class="fas fa-user-tag"></i> 角色管理
                                </button>
                                <button class="btn btn-primary" onclick="addUser()">
                                    <i class="fas fa-plus"></i> 新增用户
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 统计卡片 -->
                    <div class="row stats-cards">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-primary">68</div>
                                <div class="stats-label">用户总数</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-success">62</div>
                                <div class="stats-label">活跃用户</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-warning">5</div>
                                <div class="stats-label">待审核</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-number text-danger">1</div>
                                <div class="stats-label">已禁用</div>
                            </div>
                        </div>
                    </div>

                    <!-- 搜索筛选 -->
                    <div class="search-filters">
                        <form class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">用户名</label>
                                <input type="text" class="form-control" placeholder="请输入用户名">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">用户角色</label>
                                <select class="form-select">
                                    <option value="">全部角色</option>
                                    <option value="admin">系统管理员</option>
                                    <option value="company">企业用户</option>
                                    <option value="inspector">检查员</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">用户状态</label>
                                <select class="form-select">
                                    <option value="">全部状态</option>
                                    <option value="1">正常</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">注册时间</label>
                                <select class="form-select">
                                    <option value="">全部时间</option>
                                    <option value="today">今天</option>
                                    <option value="week">本周</option>
                                    <option value="month">本月</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    <button type="reset" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo"></i> 重置
                                    </button>
                                    <button type="button" class="btn btn-outline-success">
                                        <i class="fas fa-download"></i> 导出
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 用户列表 -->
                    <div class="user-table">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>用户信息</th>
                                        <th>所属企业</th>
                                        <th>用户角色</th>
                                        <th>联系方式</th>
                                        <th>状态</th>
                                        <th>注册时间</th>
                                        <th>最后登录</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-user-circle fa-2x text-primary"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">admin</div>
                                                    <small class="text-muted">系统管理员</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>-</td>
                                        <td><span class="role-badge role-admin">系统管理员</span></td>
                                        <td><EMAIL></td>
                                        <td><span class="user-status-badge status-active">正常</span></td>
                                        <td>2024-01-01</td>
                                        <td>2024-02-20 09:30</td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewUser(1)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="editUser(1)">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-user-circle fa-2x text-success"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">company001</div>
                                                    <small class="text-muted">张三</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>上海某幕墙材料有限公司</td>
                                        <td><span class="role-badge role-company">企业用户</span></td>
                                        <td><EMAIL></td>
                                        <td><span class="user-status-badge status-active">正常</span></td>
                                        <td>2024-01-15</td>
                                        <td>2024-02-19 16:45</td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewUser(2)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="editUser(2)">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" onclick="resetPassword(2)">
                                                    <i class="fas fa-key"></i> 重置密码
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-user-circle fa-2x text-warning"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">inspector001</div>
                                                    <small class="text-muted">李工程师</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>上海某检测公司</td>
                                        <td><span class="role-badge role-inspector">检查员</span></td>
                                        <td><EMAIL></td>
                                        <td><span class="user-status-badge status-active">正常</span></td>
                                        <td>2024-01-20</td>
                                        <td>2024-02-18 14:20</td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewUser(3)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="editUser(3)">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="disableUser(3)">
                                                    <i class="fas fa-ban"></i> 禁用
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <nav aria-label="用户列表分页">
                            <ul class="pagination justify-content-center">
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1">上一页</a>
                                </li>
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                <li class="page-item"><a class="page-link" href="#">3</a></li>
                                <li class="page-item">
                                    <a class="page-link" href="#">下一页</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增用户模态框 -->
    <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus"></i> 新增用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">用户名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">真实姓名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">邮箱地址 <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">手机号码 <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">用户角色 <span class="text-danger">*</span></label>
                                <select class="form-select" required>
                                    <option value="">请选择角色</option>
                                    <option value="admin">系统管理员</option>
                                    <option value="company">企业用户</option>
                                    <option value="inspector">检查员</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">所属企业</label>
                                <select class="form-select">
                                    <option value="">请选择企业</option>
                                    <option value="1">上海某幕墙材料有限公司</option>
                                    <option value="2">上海某建筑幕墙工程有限公司</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">初始密码 <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">确认密码 <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">备注</label>
                            <textarea class="form-control" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveUser()">保存用户</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 新增用户
        function addUser() {
            const modal = new bootstrap.Modal(document.getElementById('userModal'));
            modal.show();
        }

        // 查看用户
        function viewUser(id) {
            alert('查看用户详情');
        }

        // 编辑用户
        function editUser(id) {
            alert('编辑用户信息');
        }

        // 重置密码
        function resetPassword(id) {
            if (confirm('确认重置该用户密码？')) {
                alert('密码重置成功！新密码已发送到用户邮箱。');
            }
        }

        // 禁用用户
        function disableUser(id) {
            if (confirm('确认禁用该用户？')) {
                alert('用户已禁用！');
                location.reload();
            }
        }

        // 保存用户
        function saveUser() {
            alert('用户创建成功！');
            bootstrap.Modal.getInstance(document.getElementById('userModal')).hide();
            location.reload();
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stats-card, .search-filters, .user-table');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
